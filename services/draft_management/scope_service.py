from sqlalchemy.orm import Session
from sqlalchemy import select, and_, or_
from typing import List, Optional

from db.models import (
    EditorAccount, EditorScope, EditorRole,
    DraftExercise,
    LearningNode, Chapter, Subject
)

class ScopeService:
    """Service for handling editor scope permissions"""
    
    @staticmethod
    async def check_editor_scope(
        db: Session,
        editor: EditorAccount,
        draft: DraftExercise
    ) -> bool:
        """
        Check if an editor has permission to access a draft based on their scope.
        
        Admins always have access.
        Editors must have a scope that includes the draft's learning node.
        """
        # Admins have access to everything
        if editor.role == EditorRole.ADMIN:
            return True
        
        # Get the learning node associated with the draft
        learning_node = draft.learning_node
        if not learning_node:
            # Draft has no learning node yet - allow access for all editors
            # This enables editors to claim and work on drafts before assigning them to nodes
            return True

        # Check if any of the editor's scopes cover the draft's node
        for scope in editor.scopes:
            if ScopeService._is_node_in_scope(scope, learning_node):
                return True
        
        return False
    
    @staticmethod
    def _is_node_in_scope(scope: EditorScope, node: <PERSON>Node) -> bool:
        """Check if a learning node falls within an editor's scope"""
        # Direct node scope
        if scope.learning_node_id and scope.learning_node_id == node.id:
            return True
        
        # Chapter scope
        if scope.chapter_id and node.chapter_id == scope.chapter_id:
            return True
        
        # Subject scope
        if scope.subject_id and node.chapter and node.chapter.subject_id == scope.subject_id:
            return True
        
        return False
    
    @staticmethod
    def get_scope_filter_conditions(editor: EditorAccount) -> Optional[List]:
        """
        Get SQLAlchemy filter conditions based on editor's scopes.
        
        Returns None for admins (no filtering needed).
        Returns a list of OR conditions for editors.
        """
        if editor.role == EditorRole.ADMIN:
            return None
        
        conditions = []
        
        for scope in editor.scopes:
            if scope.learning_node_id:
                # Direct node scope
                conditions.append(
                    DraftLearningNodeExercise.learning_node_id == scope.learning_node_id
                )
            elif scope.chapter_id:
                # Chapter scope - node must be in this chapter
                conditions.append(
                    LearningNode.chapter_id == scope.chapter_id
                )
            elif scope.subject_id:
                # Subject scope - node's chapter must be in this subject
                conditions.append(
                    LearningNode.chapter.has(Chapter.subject_id == scope.subject_id)
                )
        
        # If editor has no scopes, return a condition that filters out everything
        if not conditions:
            from sqlalchemy import false
            conditions.append(false())
        
        return conditions
    
    @staticmethod
    def get_editor_accessible_nodes(
        db: Session,
        editor: EditorAccount
    ) -> List[int]:
        """Get list of learning node IDs accessible to an editor"""
        if editor.role == EditorRole.ADMIN:
            # Admin can access all nodes
            stmt = select(LearningNode.id)
            result = db.execute(stmt)
            return [row[0] for row in result]
        
        node_ids = set()
        
        for scope in editor.scopes:
            if scope.learning_node_id:
                # Direct node access
                node_ids.add(scope.learning_node_id)
            elif scope.chapter_id:
                # All nodes in chapter
                stmt = select(LearningNode.id).where(
                    LearningNode.chapter_id == scope.chapter_id
                )
                result = db.execute(stmt)
                node_ids.update([row[0] for row in result])
            elif scope.subject_id:
                # All nodes in subject's chapters
                stmt = select(LearningNode.id).join(Chapter).where(
                    Chapter.subject_id == scope.subject_id
                )
                result = db.execute(stmt)
                node_ids.update([row[0] for row in result])
        
        return list(node_ids)
    
    @staticmethod
    async def editor_has_scope_for_draft(
        db: Session,
        editor: EditorAccount,
        draft: DraftExercise
    ) -> bool:
        """
        Check if an editor (usually admin) has scope for a draft.
        This is an alias for check_editor_scope for consistency.
        """
        return await ScopeService.check_editor_scope(db, editor, draft)