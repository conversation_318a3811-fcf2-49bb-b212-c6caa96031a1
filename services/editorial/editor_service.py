from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_, and_, func, case
from typing import Dict, Any, List
from db.models import (
    EditorAccount, EditorScope, LearningNode, Chapter, Subject,
    DraftExercise, DraftLearningNodeExercise, DraftExerciseStatus
)
from loguru import logger

class EditorService:
    """Service for editor-specific operations"""
    
    @staticmethod
    async def get_current_editor_info(db: Session, editor_id: int) -> Dict[str, Any]:
        """Get current editor information with scopes"""
        editor = db.query(EditorAccount).filter(
            EditorAccount.id == editor_id
        ).options(
            joinedload(EditorAccount.scopes).joinedload(EditorScope.subject),
            joinedload(EditorAccount.scopes).joinedload(EditorScope.chapter),
            joinedload(EditorAccount.scopes).joinedload(EditorScope.learning_node)
        ).first()
        
        if not editor:
            raise ValueError(f"Editor {editor_id} not found")
        
        # Format scopes
        scopes = []
        for scope in editor.scopes:
            scope_info = {
                "id": scope.id,
                "type": None,
                "name": None,
                "public_id": None,
                "subject_name": None,
                "subject_id": None,
                "chapter_name": None,
                "chapter_id": None
            }
            
            if scope.learning_node:
                scope_info["type"] = "LEARNING_NODE"
                scope_info["name"] = scope.learning_node.title
                scope_info["public_id"] = scope.learning_node.public_id
                # Include parent info
                if scope.learning_node.chapter:
                    scope_info["chapter_name"] = scope.learning_node.chapter.title
                    scope_info["chapter_id"] = scope.learning_node.chapter.public_id
                    if scope.learning_node.chapter.subject:
                        scope_info["subject_name"] = scope.learning_node.chapter.subject.name
                        scope_info["subject_id"] = scope.learning_node.chapter.subject.public_id
            elif scope.chapter:
                scope_info["type"] = "CHAPTER"
                scope_info["name"] = scope.chapter.title
                scope_info["public_id"] = scope.chapter.public_id
                # For CHAPTER scopes, populate chapter info
                scope_info["chapter_name"] = scope.chapter.title
                scope_info["chapter_id"] = scope.chapter.public_id
                # Include parent info
                if scope.chapter.subject:
                    scope_info["subject_name"] = scope.chapter.subject.name
                    scope_info["subject_id"] = scope.chapter.subject.public_id
            elif scope.subject:
                scope_info["type"] = "SUBJECT"
                scope_info["name"] = scope.subject.name
                scope_info["public_id"] = scope.subject.public_id
                # For SUBJECT scopes, populate subject_name and subject_id
                scope_info["subject_name"] = scope.subject.name
                scope_info["subject_id"] = scope.subject.public_id
            
            scopes.append(scope_info)
        
        return {
            "id": editor.id,
            "public_id": editor.public_id,
            "email": editor.email,
            "role": editor.role.value,
            "is_active": editor.is_active,
            "created_at": editor.created_at,
            "scopes": scopes
        }
    
    @staticmethod
    async def get_assigned_learning_nodes(db: Session, editor_id: int) -> Dict[str, Any]:
        """Get all learning nodes assigned to an editor based on their scopes"""
        # Get editor with all scopes
        editor = db.query(EditorAccount).filter(
            EditorAccount.id == editor_id
        ).options(
            joinedload(EditorAccount.scopes)
        ).first()
        
        if not editor:
            raise ValueError(f"Editor {editor_id} not found")
        
        # Build query for learning nodes based on scopes
        learning_node_conditions = []
        
        for scope in editor.scopes:
            if scope.learning_node_id:
                # Specific learning node scope
                learning_node_conditions.append(
                    LearningNode.id == scope.learning_node_id
                )
            elif scope.chapter_id:
                # All learning nodes in this chapter
                learning_node_conditions.append(
                    LearningNode.chapter_id == scope.chapter_id
                )
            elif scope.subject_id:
                # All learning nodes in all chapters of this subject
                learning_node_conditions.append(
                    and_(
                        Chapter.subject_id == scope.subject_id,
                        LearningNode.chapter_id == Chapter.id
                    )
                )
        
        if not learning_node_conditions:
            # No scopes defined, return empty
            return {
                "total_nodes": 0,
                "subjects": []
            }
        
        # Query all accessible learning nodes
        query = db.query(LearningNode).join(Chapter).join(Subject)
        
        if len(learning_node_conditions) == 1:
            query = query.filter(learning_node_conditions[0])
        else:
            query = query.filter(or_(*learning_node_conditions))
        
        # Load with relationships
        nodes = query.options(
            joinedload(LearningNode.chapter).joinedload(Chapter.subject)
        ).all()
        
        # Get draft counts for all learning nodes efficiently
        node_ids = [node.id for node in nodes]
        draft_counts_dict = {}
        
        if node_ids:
            # Query draft counts grouped by learning node and status
            draft_counts_query = db.query(
                DraftLearningNodeExercise.learning_node_id,
                func.count(case(
                    (DraftExercise.status == DraftExerciseStatus.NEW, 1),
                    else_=None
                )).label('new_count'),
                func.count(case(
                    (DraftExercise.status == DraftExerciseStatus.IN_REVIEW, 1),
                    else_=None
                )).label('in_review_count'),
                func.count(DraftExercise.id).label('total_count')
            ).join(
                DraftExercise,
                DraftLearningNodeExercise.draft_exercise_id == DraftExercise.id
            ).filter(
                DraftLearningNodeExercise.learning_node_id.in_(node_ids),
                DraftExercise.status.in_([DraftExerciseStatus.NEW, DraftExerciseStatus.IN_REVIEW])
            ).group_by(
                DraftLearningNodeExercise.learning_node_id
            ).all()
            
            # Convert to dictionary for O(1) lookup
            for row in draft_counts_query:
                draft_counts_dict[row.learning_node_id] = {
                    'new_count': row.new_count or 0,
                    'in_review_count': row.in_review_count or 0,
                    'total_count': row.total_count or 0
                }
        
        # Group by subject and chapter
        subjects_dict = {}
        
        for node in nodes:
            subject = node.chapter.subject
            chapter = node.chapter
            
            # Initialize subject if not exists
            if subject.public_id not in subjects_dict:
                subjects_dict[subject.public_id] = {
                    "id": subject.public_id,
                    "name": subject.name,
                    "order": 0,  # Will be set later based on position
                    "chapters": {}
                }
            
            # Initialize chapter if not exists
            if chapter.public_id not in subjects_dict[subject.public_id]["chapters"]:
                subjects_dict[subject.public_id]["chapters"][chapter.public_id] = {
                    "id": chapter.public_id,
                    "title": chapter.title,
                    "order": chapter.ordering or 0,
                    "learning_nodes": []
                }
            
            # Get draft counts for this node
            draft_info = draft_counts_dict.get(node.id, {
                'new_count': 0,
                'in_review_count': 0,
                'total_count': 0
            })
            
            # Add learning node
            subjects_dict[subject.public_id]["chapters"][chapter.public_id]["learning_nodes"].append({
                "id": node.public_id,
                "title": node.title,
                "order": node.ordering,
                "exercises_count": len(node.exercise_associations) if hasattr(node, 'exercise_associations') else 0,
                "draft_count": draft_info['total_count'],
                "new_drafts": draft_info['new_count'],
                "in_review_drafts": draft_info['in_review_count']
            })
        
        # Convert to list format
        subjects = []
        for subject_data in subjects_dict.values():
            chapters = []
            for chapter_data in subject_data["chapters"].values():
                # Sort learning nodes by order (handle None values by treating them as 0)
                chapter_data["learning_nodes"].sort(key=lambda x: x["order"] if x["order"] is not None else 0)
                chapters.append(chapter_data)

            # Sort chapters by order (handle None values by treating them as 0)
            chapters.sort(key=lambda x: x["order"] if x["order"] is not None else 0)
            subject_data["chapters"] = chapters
            subjects.append(subject_data)
        
        # Sort subjects by name and set order
        subjects.sort(key=lambda x: x["name"])
        for idx, subject in enumerate(subjects):
            subject["order"] = idx + 1
        
        return {
            "total_nodes": len(nodes),
            "subjects": subjects
        }