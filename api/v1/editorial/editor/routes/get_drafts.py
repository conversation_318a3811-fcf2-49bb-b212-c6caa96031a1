from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.request import DraftListQueryParams
from api.v1.editorial.editor.schemas.response import (
    DraftListResponse, DraftListItemResponse, 
    SubjectInfo, ChapterInfo, LearningNodeInfo
)
from db.models import DraftExerciseStatus, Subject, Chapter
from services.draft_management.draft_service import DraftManagementService
from loguru import logger

router = APIRouter()

@router.get(
    "/drafts",
    response_model=DraftListResponse,
    status_code=status.HTTP_200_OK,
    summary="Get Editor Drafts",
    description="Get drafts available to the current editor based on their scope"
)
async def get_editor_drafts(
    status: Optional[DraftExerciseStatus] = Query(None, description="Filter by status"),
    subject_id: Optional[str] = Query(None, description="Filter by subject public ID"),
    chapter_id: Optional[str] = Query(None, description="Filter by chapter public ID"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Get drafts available to the current editor.
    
    Drafts are filtered based on the editor's assigned scopes.
    Admins can see all drafts regardless of scope.
    """
    try:
        # Convert public IDs to internal IDs if provided
        subject_internal_id = None
        chapter_internal_id = None
        
        if subject_id:
            subject = db.query(Subject).filter_by(public_id=subject_id).first()
            if subject:
                subject_internal_id = subject.id
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Subject with ID {subject_id} not found"
                )
        
        if chapter_id:
            chapter = db.query(Chapter).filter_by(public_id=chapter_id).first()
            if chapter:
                chapter_internal_id = chapter.id
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Chapter with ID {chapter_id} not found"
                )
        
        # Get drafts
        result = await DraftManagementService.get_editor_drafts(
            db=db,
            editor=auth.editor,
            status=status,
            subject_id=subject_internal_id,
            chapter_id=chapter_internal_id,
            page=page,
            page_size=page_size
        )
        
        # Transform to response format
        draft_items = []
        for draft in result["drafts"]:
            # Get the learning node for display
            if draft.learning_node:
                node = draft.learning_node
                chapter = node.chapter
                subject = chapter.subject if chapter else None

                # Extract title from data_json
                title = draft.data_json.get("prompt", "Untitled") if draft.data_json else "Untitled"
                
                draft_items.append(DraftListItemResponse(
                    id=draft.id,
                    public_id=draft.public_id,
                    title=title,
                    subject=SubjectInfo(
                        name=subject.name if subject else "Unknown",
                        public_id=subject.public_id if subject else ""
                    ),
                    chapter=ChapterInfo(
                        title=chapter.title if chapter else "Unknown",
                        public_id=chapter.public_id if chapter else None
                    ),
                    learning_node=LearningNodeInfo(
                        title=node.title,
                        public_id=node.public_id
                    ),
                    status=draft.status,
                    updated_at=draft.updated_at,
                    assigned_editor=draft.assigned_editor.email if draft.assigned_editor else None,
                    media_count=len(draft.media_files)
                ))
        
        return DraftListResponse(
            drafts=draft_items,
            total=result["total"],
            page=result["page"],
            page_size=result["page_size"],
            total_pages=result["total_pages"]
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching drafts: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while fetching drafts"
        )