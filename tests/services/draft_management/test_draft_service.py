import pytest
import asyncio
from sqlalchemy.orm import Session
from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount,
    LearningNode
)
from services.draft_management.draft_service import DraftManagementService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from tests.fixtures.editorial_fixtures import (
    admin_account, editor_account, another_editor,
    draft_exercise_new, draft_exercise_in_review,
    draft_exercise_accepted, equation_node
)


class TestDraftManagementService:
    """Test suite for DraftManagementService"""
    
    def test_list_drafts_with_filters(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_new: DraftExercise, draft_exercise_in_review: DraftExercise
    ):
        """Test listing drafts with various filters"""
        # Test status filter
        result = asyncio.run(DraftManagementService.get_editor_drafts(
            db=db_session,
            editor=editor_account,
            status=DraftExerciseStatus.NEW
        ))
        
        assert result["total"] >= 1
        drafts_new = [d for d in result["drafts"] if d.status == DraftExerciseStatus.NEW]
        assert len(drafts_new) >= 1
        assert any(d.id == draft_exercise_new.id for d in drafts_new)
        
        # Test pagination
        result = asyncio.run(DraftManagementService.get_editor_drafts(
            db=db_session,
            editor=editor_account,
            page=1,
            page_size=1
        ))
        
        assert len(result["drafts"]) <= 1
        assert "total_pages" in result
    
    def test_list_drafts_respects_scope(
        self, db_session: Session, editor_account: EditorAccount,
        another_editor: EditorAccount, draft_exercise_new: DraftExercise
    ):
        """Test that editors only see drafts within their scope"""
        # Regular editor has scope, should see draft
        result = asyncio.run(DraftManagementService.get_editor_drafts(
            db=db_session,
            editor=editor_account
        ))
        assert result["total"] > 0
        
        # Another editor has no scope, should see no drafts
        result = asyncio.run(DraftManagementService.get_editor_drafts(
            db=db_session,
            editor=another_editor
        ))
        assert result["total"] == 0
    
    def test_claim_draft_success(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_new: DraftExercise
    ):
        """Test successful draft claiming"""
        draft = asyncio.run(DraftManagementService.claim_draft(
            db=db_session,
            draft_id=draft_exercise_new.id,
            editor=editor_account
        ))
        
        assert draft.status == DraftExerciseStatus.IN_REVIEW
        assert draft.assigned_editor_id == editor_account.id
        

    
    def test_claim_draft_already_assigned(
        self, db_session: Session, editor_account: EditorAccount,
        another_editor: EditorAccount, draft_exercise_in_review: DraftExercise
    ):
        """Test claiming a draft that's already assigned"""
        # Draft is already assigned to editor_account
        with pytest.raises(ValidationError) as exc_info:
            asyncio.run(DraftManagementService.claim_draft(
                db=db_session,
                draft_id=draft_exercise_in_review.id,
                editor=another_editor
            ))
        
        assert "already assigned" in str(exc_info.value)
    
    def test_claim_draft_no_scope(
        self, db_session: Session, another_editor: EditorAccount,
        draft_exercise_new: DraftExercise
    ):
        """Test claiming a draft without proper scope"""
        with pytest.raises(PermissionDeniedError):
            asyncio.run(DraftManagementService.claim_draft(
                db=db_session,
                draft_id=draft_exercise_new.id,
                editor=another_editor
            ))
    
    def test_update_draft_success(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test successful draft update"""
        data = {"prompt": "Updated prompt", "options": []}
        solution = {"correct_answer": {"correct_option_id": ["a"]}}
        
        draft = asyncio.run(DraftManagementService.update_draft(
            db=db_session,
            draft_id=draft_exercise_in_review.id,
            editor=editor_account,
            data=data,
            solution=solution
        ))
        
        assert draft.data_json["prompt"] == "Updated prompt"
        assert draft.solution_json["correct_answer"]["correct_option_id"] == ["a"]
        
        # TODO: Audit log verification removed - DraftExerciseAudit not implemented
        # # Verify audit log
        # from db.models import DraftExerciseAudit
        # audit = db_session.query(DraftExerciseAudit).filter_by(
        #     draft_id=draft.id,
        #     action="UPDATED"
        # ).first()
        # assert audit is not None
        # assert "data_json" in audit.details["changes"]
    
    def test_update_draft_not_assigned(
        self, db_session: Session, another_editor: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test updating a draft not assigned to the editor"""
        with pytest.raises(PermissionDeniedError):
            asyncio.run(DraftManagementService.update_draft(
                db=db_session,
                draft_id=draft_exercise_in_review.id,
                editor=another_editor,
                data={"prompt": "Hacked!"}
            ))
    
    def test_update_draft_auto_claim_new(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_new: DraftExercise
    ):
        """Test auto-claiming a NEW draft when updating"""
        # Ensure draft is NEW and unassigned
        assert draft_exercise_new.status == DraftExerciseStatus.NEW
        assert draft_exercise_new.assigned_editor_id is None
        
        # Update the draft - should auto-claim it
        new_data = {"prompt": "Updated prompt", "question": "New question"}
        draft = asyncio.run(DraftManagementService.update_draft(
            db=db_session,
            draft_id=draft_exercise_new.id,
            editor=editor_account,
            data=new_data
        ))
        
        # Verify draft was auto-claimed
        assert draft.assigned_editor_id == editor_account.id
        assert draft.status == DraftExerciseStatus.IN_REVIEW
        assert draft.data_json == new_data
        
    def test_update_draft_auto_claim_no_scope(
        self, db_session: Session, another_editor: EditorAccount
    ):
        """Test auto-claim fails without proper scope"""
        from tests.fixtures.editorial_fixtures import create_draft_without_scope
        
        # Create a draft that another_editor doesn't have scope for
        draft = create_draft_without_scope(db_session)
        
        # Ensure draft is NEW and unassigned
        assert draft.status == DraftExerciseStatus.NEW
        assert draft.assigned_editor_id is None
        
        # Try to update - should fail due to lack of scope
        with pytest.raises(PermissionDeniedError) as exc_info:
            asyncio.run(DraftManagementService.update_draft(
                db=db_session,
                draft_id=draft.id,
                editor=another_editor,
                data={"prompt": "Should not work"}
            ))
        
        assert "outside your assigned scope" in str(exc_info.value)
    
    def test_accept_draft_success(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test successful draft acceptance"""
        # Ensure draft has required data
        draft_exercise_in_review.data_json = {"prompt": "Test"}
        draft_exercise_in_review.solution_json = {"correct_answer": {}}
        db_session.commit()
        
        draft = asyncio.run(DraftManagementService.accept_draft(
            db=db_session,
            draft_id=draft_exercise_in_review.id,
            editor=editor_account
        ))
        
        assert draft.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR
        

    
    def test_accept_draft_incomplete(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test accepting an incomplete draft"""
        # Remove solution data
        draft_exercise_in_review.solution_json = None
        db_session.commit()
        
        with pytest.raises(ValidationError) as exc_info:
            asyncio.run(DraftManagementService.accept_draft(
                db=db_session,
                draft_id=draft_exercise_in_review.id,
                editor=editor_account
            ))
        
        assert "must have both exercise data and solution" in str(exc_info.value)
    
    def test_reject_draft_by_admin(
        self, db_session: Session, admin_account: EditorAccount,
        draft_exercise_accepted: DraftExercise
    ):
        """Test admin rejecting a draft"""
        rejection_reason = "Needs more detail in the explanation"
        suggested_changes = ["Add step-by-step solution", "Include visual aids"]
        
        draft = asyncio.run(DraftManagementService.reject_draft_by_admin(
            db=db_session,
            draft_id=draft_exercise_accepted.id,
            admin=admin_account,
            rejection_reason=rejection_reason,
            suggested_changes=suggested_changes
        ))
        
        assert draft.status == DraftExerciseStatus.REJECTED_BY_ADMIN
        assert draft.reject_reason == rejection_reason
        assert draft.data_json["metadata"]["suggested_changes"] == suggested_changes
        
        # TODO: Audit log verification removed - DraftExerciseAudit not implemented
        # # Verify audit log
        # from db.models import DraftExerciseAudit
        # audit = db_session.query(DraftExerciseAudit).filter_by(
        #     draft_id=draft.id,
        #     action="REJECTED_BY_ADMIN"
        # ).first()
        # assert audit is not None
        # assert audit.details["rejection_reason"] == rejection_reason
    
    def test_reject_draft_non_admin(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_accepted: DraftExercise
    ):
        """Test non-admin trying to reject a draft"""
        with pytest.raises(PermissionDeniedError) as exc_info:
            asyncio.run(DraftManagementService.reject_draft_by_admin(
                db=db_session,
                draft_id=draft_exercise_accepted.id,
                admin=editor_account,
                rejection_reason="Not allowed"
            ))
        
        assert "Only admins" in str(exc_info.value)
    
    def test_get_draft_detail(
        self, db_session: Session, editor_account: EditorAccount,
        draft_exercise_in_review: DraftExercise, equation_node: LearningNode
    ):
        """Test getting draft details"""
        # Association should already exist from the fixture
        draft = asyncio.run(DraftManagementService.get_draft_detail(
            db=db_session,
            draft_id=draft_exercise_in_review.id,
            editor=editor_account
        ))
        
        assert draft.id == draft_exercise_in_review.id
        assert len(draft.learning_node_associations) > 0
        assert draft.assigned_editor is not None
    
    def test_get_draft_detail_no_access(
        self, db_session: Session, another_editor: EditorAccount,
        draft_exercise_in_review: DraftExercise
    ):
        """Test getting draft details without access"""
        with pytest.raises(PermissionDeniedError):
            asyncio.run(DraftManagementService.get_draft_detail(
                db=db_session,
                draft_id=draft_exercise_in_review.id,
                editor=another_editor
            ))