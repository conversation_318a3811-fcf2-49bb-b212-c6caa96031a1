import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, UTC
import jwt
from core.config import settings

from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount, EditorRole,
    LearningNode, EditorScope
)
from tests.fixtures.editorial_fixtures import (
    admin_account, editor_account,
    draft_exercise_accepted, equation_node
)
from tests.utils.editorial_test_utils import create_editor_token


class TestAdminEndpoints:
    """Test suite for admin API endpoints"""
    
    @pytest.fixture
    def admin_headers(self, admin_account: EditorAccount):
        """Create auth headers for admin"""
        token = create_editor_token(
            editor_id=admin_account.id,
            email=admin_account.email,
            role=admin_account.role
        )
        return {"x-editor-token": token}

    @pytest.fixture
    def editor_headers(self, editor_account: EditorAccount):
        """Create auth headers for regular editor"""
        token = create_editor_token(
            editor_id=editor_account.id,
            email=editor_account.email,
            role=editor_account.role
        )
        return {"x-editor-token": token}
    
    def test_publish_draft(
        self, client, db_session: Session, mocker,
        admin_headers: dict, draft_exercise_accepted: DraftExercise,
        equation_node: LearningNode, admin_account: EditorAccount
    ):
        """Test publishing a draft"""
        # Mock the publishing service to avoid S3/R2 calls
        mock_publish = mocker.AsyncMock(
            return_value={
                "exercise_id": 999,
                "published_at": datetime.now(UTC),
                "batch_id": None
            }
        )
        mocker.patch(
            "services.draft_management.publishing_service.PublishingService.publish_draft",
            mock_publish
        )
        
        # Create admin scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, admin_account, learning_node=equation_node)
        
        # Set up draft with proper solution data
        draft_exercise_accepted.data_json = {
            "prompt": "What is 2 + 2?",
            "options": [
                {"public_id": "opt1", "text": "3"},
                {"public_id": "opt2", "text": "4"},
                {"public_id": "opt3", "text": "5"}
            ]
        }
        draft_exercise_accepted.solution_json = {
            "correct_answer": {"correct_option_id": ["opt2"]},
            "solution_steps": [{"text": "2 + 2 equals 4."}]
        }
        
        # Note: Association is already created by the fixture
        db_session.commit()
        
        response = client.post(
            f"/api/v1/editorial/admin/drafts/{draft_exercise_accepted.public_id}/publish",
            json={"publish_notes": "Looks good!"},
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "exercise_id" in data
        assert "published_at" in data
    
    def test_reject_draft(
        self, client: TestClient, admin_headers: dict,
        draft_exercise_accepted: DraftExercise
    ):
        """Test rejecting a draft"""
        response = client.post(
            f"/api/v1/editorial/admin/drafts/{draft_exercise_accepted.id}/reject",
            json={
                "rejection_reason": "The explanation needs more detail",
                "suggested_changes": ["Add step-by-step solution", "Include visual aids"]
            },
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["draft"]["status"] == "REJECTED_BY_ADMIN"
    
    def test_bulk_publish(
        self, client: TestClient, db_session: Session, mocker,
        admin_headers: dict, equation_node: LearningNode, admin_account: EditorAccount
    ):
        """Test bulk publishing drafts"""
        # Mock the bulk publish service to avoid S3/R2 calls
        mock_bulk_publish = mocker.AsyncMock(
            return_value={
                "batch_id": 123,
                "successful_count": 2,
                "failed_count": 0,
                "results": {
                    3: {"success": True, "exercise_id": 100},
                    4: {"success": True, "exercise_id": 101}
                }
            }
        )
        mocker.patch(
            "services.draft_management.publishing_service.PublishingService.bulk_publish",
            mock_bulk_publish
        )
        
        # Create admin scope
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, admin_account, learning_node=equation_node)
        
        # Create multiple accepted drafts
        drafts = []
        for i in range(2):
            draft = DraftExercise(
                public_id=f"bulk-draft-{i}",
                exercise_type="mc-simple",
                difficulty="medium",
                data_json={
                    "prompt": f"Question {i}",
                    "options": [
                        {"public_id": "opt1", "text": "Option A"},
                        {"public_id": "opt2", "text": "Option B"}
                    ]
                },
                solution_json={
                    "correct_answer": {"correct_option_id": ["opt1"]},
                    "solution_steps": [{"text": f"Answer to question {i}"}]
                },
                status=DraftExerciseStatus.ACCEPTED_BY_EDITOR,
                learning_node_id=equation_node.id
            )
            db_session.add(draft)
            db_session.flush()
            drafts.append(draft)
        
        db_session.commit()
        
        response = client.post(
            "/api/v1/editorial/admin/drafts/bulk-publish",
            json={
                "draft_ids": [d.id for d in drafts],
                "batch_name": "Test batch",
                "batch_notes": "Testing bulk publish"
            },
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["successful_publishes"] == 2
    
    def test_non_admin_cannot_publish(
        self, client: TestClient, db_session: Session,
        editor_headers: dict, editor_account: EditorAccount,
        draft_exercise_accepted: DraftExercise, equation_node: LearningNode
    ):
        """Test that non-admin cannot publish"""
        # Create editor scope (not admin)
        from tests.fixtures.editorial_fixtures import EditorFixtures
        EditorFixtures.create_editor_scope(db_session, editor_account, learning_node=equation_node)
        
        # Note: Association is already created by the fixture
        
        response = client.post(
            f"/api/v1/editorial/admin/drafts/{draft_exercise_accepted.public_id}/publish",
            json={"publish_notes": "Unauthorized"},
            headers=editor_headers
        )
        
        assert response.status_code == 401  # RequireAdmin returns 401 not 403 for non-admin users
    
    def test_list_editors(
        self, client: TestClient, admin_headers: dict,
        editor_account: EditorAccount
    ):
        """Test listing editor accounts"""
        response = client.get(
            "/api/v1/editorial/admin/editors",
            headers=admin_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert "editors" in data
        assert data["total"] >= 1

        # Check editor structure
        editor = data["editors"][0]
        assert "id" in editor
        assert "email" in editor
        assert "role" in editor
        assert "scopes" in editor
    
    def test_get_editor(
        self, client: TestClient, admin_headers: dict,
        editor_account: EditorAccount
    ):
        """Test getting a specific editor by public ID"""
        response = client.get(
            f"/api/v1/editorial/admin/editors/{editor_account.public_id}",
            headers=admin_headers
        )

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == editor_account.id
        assert data["public_id"] == editor_account.public_id
        assert data["email"] == editor_account.email
        assert data["role"] == editor_account.role.value
        assert data["is_active"] == editor_account.is_active
        assert "scopes" in data
        assert isinstance(data["scopes"], list)
        # Verify no full_name field is present
        assert "full_name" not in data

    def test_create_editor(
        self, client: TestClient, admin_headers: dict
    ):
        """Test creating a new editor account"""
        response = client.post(
            "/api/v1/editorial/admin/editors",
            json={
                "email": "<EMAIL>",
                "role": "EDITOR"
            },
            headers=admin_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["email"] == "<EMAIL>"
        assert data["role"] == "EDITOR"
        # Verify no full_name field is present
        assert "full_name" not in data
    
    @pytest.mark.skip(reason="Scope management endpoint not implemented")
    def test_add_editor_scope(
        self, client: TestClient, db_session: Session,
        admin_headers: dict, editor_account: EditorAccount
    ):
        """Test adding scope to an editor"""
        response = client.post(
            f"/api/v1/editorial/admin/editors/{editor_account.public_id}/scopes",
            json={
                "scope_type": "SUBJECT",
                "scope_value": "SCIENCE"
            },
            headers=admin_headers
        )

        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert data["scope"]["scope_value"] == "SCIENCE"
    
    @pytest.mark.skip(reason="Audit endpoint not implemented")
    def test_query_audit_logs(
        self, client: TestClient, admin_headers: dict
    ):
        """Test querying audit logs"""
        response = client.post(
            "/api/v1/editorial/admin/audit/query",
            json={
                "page": 1,
                "limit": 20
            },
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "logs" in data
        assert "total" in data
        assert "page" in data
    
    def test_get_metrics_overview(
        self, client: TestClient, admin_headers: dict
    ):
        """Test getting metrics overview"""
        response = client.post(
            "/api/v1/editorial/admin/metrics/overview",
            json={
                "metric_type": "overview"
            },
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "draft_metrics" in data
        assert "editor_metrics" in data
        assert "publishing_metrics" in data
    
    def test_cleanup_report(
        self, client: TestClient, admin_headers: dict
    ):
        """Test generating cleanup report"""
        response = client.get(
            "/api/v1/editorial/admin/cleanup/report",
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "cleanup_candidates" in data["report"]
        assert "current_statistics" in data["report"]
    
    def test_run_cleanup_tasks(
        self, client: TestClient, admin_headers: dict
    ):
        """Test running cleanup tasks"""
        response = client.post(
            "/api/v1/editorial/admin/cleanup/assignments",
            params={"abandoned_days": 7},
            headers=admin_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "released_count" in data["details"]