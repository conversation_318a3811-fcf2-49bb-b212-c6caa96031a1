import { z } from 'zod';
import {
  GetDraftsResponseSchema,
  GetDraftDetailResponseSchema,
  CreateDraftRequestSchema,
  UpdateDraftRequestSchema,
  AcceptDraftRequestSchema,
  DraftListItemApp
} from '@/schemas/internal/draftSchema';
import {
  MetricsOverviewResponseSchema,
  BulkPublishResponseSchema
} from '@/schemas/internal/adminSchema';
import {
  DraftExerciseStatusEnum,
  DraftMediaTypeEnum,

  ExerciseTypeEnum,
  DifficultyEnum,
  type DraftExerciseStatus,
  type DraftMediaType,
  type ExerciseType,
  type Difficulty
} from '@/types/internal/enums';

// Re-export enums for backward compatibility
export {
  DraftExerciseStatusEnum,
  DraftMediaTypeEnum,

  ExerciseTypeEnum,
  DifficultyEnum,
  type DraftExerciseStatus,
  type DraftMediaType,

  type ExerciseType,
  type Difficulty
};

// Schema-inferred types from existing schemas
export type DraftExercise = z.infer<typeof GetDraftDetailResponseSchema.frontend>; // Simplified draft for editing
export type DraftListItem = DraftListItemApp; // Simplified draft for lists
export type DraftsList = z.infer<typeof GetDraftsResponseSchema.frontend>;
export type CreateDraftRequest = z.infer<typeof CreateDraftRequestSchema.frontend>;
export type UpdateDraftRequest = z.infer<typeof UpdateDraftRequestSchema.frontend>;
export type AcceptDraftRequest = z.infer<typeof AcceptDraftRequestSchema.frontend>;

// Extract nested types from the list item schema (which has the complex nested structures)
export type LearningNodeReference = NonNullable<DraftListItem['learningNode']>;
export type DraftMediaFile = NonNullable<DraftListItem['mediaFiles']>[0];
// Note: AssignedEditor is not available in simplified schemas
// These would need to be fetched separately if needed for admin functionality

// Admin types from schemas
export type OverviewMetrics = z.infer<typeof MetricsOverviewResponseSchema.frontend>;
export type BatchPublishResult = z.infer<typeof BulkPublishResponseSchema.frontend>;

// Utility types for draft management
export interface DraftFilters {
  status?: DraftExerciseStatus;
  subjectId?: number;
  chapterId?: number;
  learningNodeId?: number;
  editorId?: number;
  exerciseType?: ExerciseType;
  difficulty?: Difficulty;
  dateFrom?: string;
  dateTo?: string;
}

// Paginated response structure (generic utility)
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Editor Performance Metrics (to be moved to schema when admin service is fixed)
export interface EditorPerformanceMetrics {
  editorId: number;
  editorName: string;
  period: {
    from: string;
    to: string;
  };
  draftsReviewed: number;
  draftsAccepted: number;
  draftsRejected: number;
  averageReviewTimeHours: number;
  acceptanceRate: number;
  currentAssignments: number;
}

// Draft grouping for admin review (UI-specific structure)
export interface DraftsBySubject {
  [subjectName: string]: {
    subjectId: number;
    chapters: {
      [chapterTitle: string]: {
        chapterId: number;
        draftCount: number;
        drafts: DraftListItem[];
      };
    };
  };
}