import { z } from 'zod';
import { createSchemaPair } from '@/schemas/schemaUtils';
import {
  DraftExerciseStatusEnum,
  ExerciseTypeEnum,
  DifficultyEnum,
  DraftMediaTypeEnum,

} from '@/types/internal/enums';
import { EditorUserSchema } from './authSchema';
import {
  exerciseDataSchemaMap,
  validateExerciseData,
  validateExerciseSolution,
  ExerciseSolutionSchema
} from './exerciseDataValidation';

// Exercise type mapping from backend (legacy) to frontend (new) format
const EXERCISE_TYPE_MAPPING: Record<string, z.infer<typeof ExerciseTypeEnum>> = {
  // Legacy backend values -> New frontend values
  'multiple_choice': 'mc-simple', // Default to simple, will need context to determine multi
  'true_false': 'true-false',
  'error_correction': 'error-correction',
  'matching_pairs': 'matching-pairs',
  // New values (pass through)
  'mc-simple': 'mc-simple',
  'mc-multi': 'mc-multi',
  'true-false': 'true-false',
  'error-correction': 'error-correction',
  'matching-pairs': 'matching-pairs',
  'cloze': 'cloze',
  'dropdown': 'dropdown',
  'input': 'input',
  'categorize': 'categorize',
  'highlight': 'highlight',
};

// Function to transform exercise type from backend to frontend
function transformExerciseType(backendType: string | undefined, dataJson?: any): z.infer<typeof ExerciseTypeEnum> {
  // Handle undefined or null backend type with a fallback
  if (!backendType) {
    console.warn('Exercise type is undefined, falling back to mc-simple');
    return 'mc-simple';
  }

  // Special handling for multiple_choice - determine if it's simple or multi based on data
  if (backendType === 'multiple_choice') {
    // Check if the exercise allows multiple selections
    const allowMultiple = dataJson?.allowMultiple || dataJson?.multipleSelect || false;
    return allowMultiple ? 'mc-multi' : 'mc-simple';
  }

  return EXERCISE_TYPE_MAPPING[backendType] || backendType as z.infer<typeof ExerciseTypeEnum>;
}

// Learning Node Reference Schema
const LearningNodeReferenceSchemaBase = z.object({
  id: z.number(),
  title: z.string(),
  publicId: z.string(),
  chapter: z.object({
    id: z.number(),
    title: z.string(),
    publicId: z.string(),
    subject: z.object({
      id: z.number(),
      name: z.string(),
      publicId: z.string(),
    }),
  }),
});

// Draft Media File Schema
const DraftMediaFileSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  mediaType: DraftMediaTypeEnum,
  url: z.string().url(),
  storagePath: z.string(),
  originalFilename: z.string(),
  contentType: z.string(),
  fileSize: z.number(),
  metadata: z.object({
    width: z.number().optional(),
    height: z.number().optional(),
    duration: z.number().optional(),
  }).optional(),
  createdAt: z.string(),
});



// Assigned Editor Schema
const AssignedEditorSchemaBase = z.object({
  id: z.number(),
  email: z.string().email(),
  name: z.string().optional(),
});

// Validation function for draft data based on exercise type
function createValidatedDataJsonSchema(exerciseType: z.infer<typeof ExerciseTypeEnum>) {
  return z.any().refine((data) => {
    try {
      validateExerciseData(exerciseType as keyof typeof exerciseDataSchemaMap, data);
      return true;
    } catch {
      return false;
    }
  }, {
    message: `Invalid data structure for exercise type: ${exerciseType}`,
  });
}

function createValidatedSolutionJsonSchema() {
  return z.any().refine((data) => {
    try {
      validateExerciseSolution(data);
      return true;
    } catch {
      return false;
    }
  }, {
    message: 'Invalid solution structure',
  });
}

// Draft Exercise Schemas
const DraftExerciseSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  exerciseType: ExerciseTypeEnum,
  difficulty: DifficultyEnum,
  dataJson: z.record(z.any()), // Will be validated based on exerciseType in practice
  solutionJson: z.record(z.any()), // Will be validated against ExerciseSolutionSchema in practice
  status: DraftExerciseStatusEnum,
  assignedEditorId: z.number().nullable(),
  assignedEditor: AssignedEditorSchemaBase.optional(),
  rejectReason: z.string().nullable(),
  publishedExerciseId: z.number().nullable(),
  sourceExerciseId: z.number().nullable().optional(),
  learningNode: LearningNodeReferenceSchemaBase,
  mediaFiles: z.array(DraftMediaFileSchemaBase),

  createdAt: z.string(),
  updatedAt: z.string(),
  acceptedAt: z.string().optional(),
  publishedAt: z.string().optional(),
});

const DraftExerciseApiSchemaBase = z.object({
  id: z.number(),
  public_id: z.string(),
  exercise_type: ExerciseTypeEnum,
  difficulty: DifficultyEnum,
  data_json: z.record(z.any()),
  solution_json: z.record(z.any()),
  status: DraftExerciseStatusEnum,
  assigned_editor_id: z.number().nullable(),
  assigned_editor: z.object({
    id: z.number(),
    email: z.string().email(),
    name: z.string().optional(),
  }).optional(),
  reject_reason: z.string().nullable(),
  published_exercise_id: z.number().nullable(),
  source_exercise_id: z.number().nullable().optional(),
  learning_node: z.object({
    id: z.number(),
    title: z.string(),
    public_id: z.string(),
    chapter: z.object({
      id: z.number(),
      title: z.string(),
      public_id: z.string(),
      subject: z.object({
        id: z.number(),
        name: z.string(),
        public_id: z.string(),
      }),
    }),
  }),
  media_files: z.array(z.object({
    id: z.number(),
    public_id: z.string(),
    media_type: DraftMediaTypeEnum,
    url: z.string().url(),
    storage_path: z.string(),
    original_filename: z.string(),
    content_type: z.string(),
    file_size: z.number(),
    metadata: z.object({
      width: z.number().optional(),
      height: z.number().optional(),
      duration: z.number().optional(),
    }).optional(),
    created_at: z.string(),
  })),

  created_at: z.string(),
  updated_at: z.string(),
  accepted_at: z.string().optional(),
  published_at: z.string().optional(),
});

// Get Drafts Request Schemas
const GetDraftsRequestAppSchema = z.object({
  status: DraftExerciseStatusEnum.optional(),
  subjectId: z.number().optional(),
  chapterId: z.number().optional(),
  learningNodeId: z.number().optional(),
  editorId: z.number().optional(),
  exerciseType: ExerciseTypeEnum.optional(),
  difficulty: DifficultyEnum.optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(50),
});

const GetDraftsRequestApiSchema = z.object({
  status: DraftExerciseStatusEnum.optional(),
  subject_id: z.number().optional(),
  chapter_id: z.number().optional(),
  learning_node_id: z.number().optional(),
  editor_id: z.number().optional(),
  exercise_type: ExerciseTypeEnum.optional(),
  difficulty: DifficultyEnum.optional(),
  date_from: z.string().optional(),
  date_to: z.string().optional(),
  page: z.number().min(1).default(1),
  page_size: z.number().min(1).max(100).default(50),
});

export const GetDraftsRequestSchema = createSchemaPair(
  GetDraftsRequestAppSchema,
  GetDraftsRequestApiSchema
);

// Simplified Draft Schema for List View (only essential fields that backend provides)
const DraftListItemSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  exerciseType: ExerciseTypeEnum, // Always defined after transformation with fallback
  difficulty: DifficultyEnum.optional(),
  status: DraftExerciseStatusEnum,
  dataJson: z.record(z.any()).optional(),
  solutionJson: z.record(z.any()).optional(),
  assignedEditorId: z.number().nullable().optional(),
  assignedEditor: z.string().optional(), // Backend returns string, not object
  rejectReason: z.string().nullable().optional(),
  publishedExerciseId: z.number().nullable().optional(),
  learningNode: z.object({
    title: z.string(),
    publicId: z.string(),
  }).optional(),
  subject: z.object({
    name: z.string(),
    publicId: z.string(),
  }).optional(),
  chapter: z.object({
    title: z.string(),
    publicId: z.string(),
  }).optional(),
  mediaFiles: z.array(z.object({
    id: z.number(),
    publicId: z.string(),
    mediaType: DraftMediaTypeEnum,
    url: z.string().url(),
    originalFilename: z.string(),
    contentType: z.string(),
    fileSize: z.number(),
    createdAt: z.string(),
  })),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
});

const DraftListItemApiSchemaBase = z.object({
  id: z.number(),
  public_id: z.string(),
  title: z.string().optional(), // Backend provides title
  subject: z.object({
    name: z.string(),
    public_id: z.string(),
  }).optional(),
  chapter: z.object({
    title: z.string(),
    public_id: z.string().nullable(),
  }).optional(),
  learning_node: z.object({ // Backend returns learning_node (singular)
    title: z.string(),
    public_id: z.string(),
  }).optional(),
  exercise_type: ExerciseTypeEnum.optional(), // Backend might not always provide this
  difficulty: DifficultyEnum.optional(),
  status: DraftExerciseStatusEnum,
  data_json: z.record(z.any()).optional(),
  solution_json: z.record(z.any()).optional(),
  assigned_editor_id: z.number().nullable().optional(),
  assigned_editor: z.string().nullable().optional(), // Backend returns string (email)
  reject_reason: z.string().nullable().optional(),
  published_exercise_id: z.number().nullable().optional(),
  media_count: z.number().optional(), // Backend provides media_count
  updated_at: z.string().optional(),
  created_at: z.string().optional(),
  // Legacy fields that might be present but we don't use
  learning_nodes: z.array(z.any()).optional(), // Fallback for other endpoints
  media: z.array(z.any()).optional(), // Fallback for other endpoints

});

// Get Drafts Response Schemas
const GetDraftsResponseAppSchema = z.object({
  drafts: z.array(DraftListItemSchemaBase),
  pagination: z.object({
    page: z.number(),
    pageSize: z.number(),
    totalItems: z.number(),
    totalPages: z.number(),
  }).optional(),
});

const GetDraftsResponseApiSchema = z.object({
  drafts: z.array(DraftListItemApiSchemaBase),
  pagination: z.object({
    page: z.number(),
    page_size: z.number(),
    total_items: z.number(),
    total_pages: z.number(),
  }).optional(),
});

// Custom schema pair for GetDrafts to handle learning_node -> learningNodes transformation
export const GetDraftsResponseSchema = {
  frontend: GetDraftsResponseAppSchema,
  api: GetDraftsResponseApiSchema,
  toApi: (data: z.infer<typeof GetDraftsResponseAppSchema>): z.infer<typeof GetDraftsResponseApiSchema> => {
    // Frontend to API transformation (not typically used for responses)
    return {
      drafts: data.drafts.map(draft => ({
        id: draft.id,
        public_id: draft.publicId,
        exercise_type: draft.exerciseType,
        difficulty: draft.difficulty,
        status: draft.status,
        data_json: draft.dataJson,
        solution_json: draft.solutionJson,
        assigned_editor_id: draft.assignedEditorId,
        assigned_editor: draft.assignedEditor,
        reject_reason: draft.rejectReason,
        published_exercise_id: draft.publishedExerciseId,
        learning_node: draft.learningNode ? {
          title: draft.learningNode.title,
          public_id: draft.learningNode.publicId,
        } : undefined,
        media: draft.mediaFiles || [],

        created_at: draft.createdAt,
        updated_at: draft.updatedAt,
      })),
      pagination: data.pagination ? {
        page: data.pagination.page,
        page_size: data.pagination.pageSize,
        total_items: data.pagination.totalItems,
        total_pages: data.pagination.totalPages,
      } : undefined,
    };
  },
  toFrontend: (data: z.infer<typeof GetDraftsResponseApiSchema>): z.infer<typeof GetDraftsResponseAppSchema> => {
    // API to Frontend transformation - handle learning_node -> learningNodes and exercise type mapping
    const parsedApi = GetDraftsResponseApiSchema.parse(data);
    return {
      drafts: parsedApi.drafts.map(draft => ({
        id: draft.id,
        publicId: draft.public_id,
        exerciseType: transformExerciseType(draft.exercise_type, draft.data_json),
        difficulty: draft.difficulty,
        status: draft.status,
        dataJson: draft.data_json,
        solutionJson: draft.solution_json,
        assignedEditorId: draft.assigned_editor_id,
        assignedEditor: draft.assigned_editor || undefined,
        rejectReason: draft.reject_reason,
        publishedExerciseId: draft.published_exercise_id,
        // Transform learning_node, subject, and chapter as separate fields
        learningNode: draft.learning_node ? {
          title: draft.learning_node.title,
          publicId: draft.learning_node.public_id,
        } : undefined,
        subject: draft.subject ? {
          name: draft.subject.name,
          publicId: draft.subject.public_id,
        } : undefined,
        chapter: draft.chapter ? {
          title: draft.chapter.title,
          publicId: draft.chapter.public_id || '',
        } : undefined,
        mediaFiles: draft.media || [],

        createdAt: draft.created_at,
        updatedAt: draft.updated_at,
      })),
      pagination: parsedApi.pagination ? {
        page: parsedApi.pagination.page,
        pageSize: parsedApi.pagination.page_size,
        totalItems: parsedApi.pagination.total_items,
        totalPages: parsedApi.pagination.total_pages,
      } : undefined,
    };
  }
};

// Get Draft Detail Request Schemas (just the ID in path)
const GetDraftDetailRequestAppSchema = z.object({}); // No request body
const GetDraftDetailRequestApiSchema = z.object({});

export const GetDraftDetailRequestSchema = createSchemaPair(
  GetDraftDetailRequestAppSchema,
  GetDraftDetailRequestApiSchema
);

// Simplified Draft Detail Schema (only essential fields for editing)
const SimplifiedDraftDetailSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  exerciseType: ExerciseTypeEnum,
  difficulty: DifficultyEnum.optional(),
  dataJson: z.record(z.any()),
  solutionJson: z.record(z.any()).optional(),
  status: DraftExerciseStatusEnum,
  rejectReason: z.string().nullable().optional(), // Only needed if draft was rejected
  createdAt: z.string(),
  updatedAt: z.string(),
});

const SimplifiedDraftDetailApiSchemaBase = z.object({
  id: z.number(),
  public_id: z.string(),
  exercise_type: ExerciseTypeEnum,
  difficulty: DifficultyEnum.optional(),
  data_json: z.record(z.any()),
  solution_json: z.record(z.any()).optional(),
  status: DraftExerciseStatusEnum,
  reject_reason: z.string().nullable().optional(), // Only needed if draft was rejected
  created_at: z.string(),
  updated_at: z.string(),
  // Handle backend fields that we don't need but might be present
  assigned_editor: z.any().optional(), // Backend returns object, we ignore it
  media: z.array(z.any()).optional(), // Backend returns media array, we ignore it

  learning_node: z.object({
    title: z.string(),
    public_id: z.string(),
  }).optional(), // Backend returns single learning_node
  published_exercise_id: z.number().nullable().optional(),
  assigned_editor_id: z.number().nullable().optional(),
});

// Get Draft Detail Response Schemas
const GetDraftDetailResponseAppSchema = SimplifiedDraftDetailSchemaBase;
const GetDraftDetailResponseApiSchema = SimplifiedDraftDetailApiSchemaBase;

// Custom transformation for draft detail response to handle exercise type mapping
export const GetDraftDetailResponseSchema = {
  frontend: GetDraftDetailResponseAppSchema,
  api: GetDraftDetailResponseApiSchema,
  toApi: (data: z.infer<typeof GetDraftDetailResponseAppSchema>): z.infer<typeof GetDraftDetailResponseApiSchema> => {
    // Transform frontend to API format (reverse mapping)
    const reverseMapping: Record<string, string> = {
      'mc-simple': 'multiple_choice',
      'mc-multi': 'multiple_choice',
      'true-false': 'true_false',
      'error-correction': 'error_correction',
      'matching-pairs': 'matching_pairs',
    };

    return {
      ...data,
      exercise_type: reverseMapping[data.exerciseType] || data.exerciseType,
      public_id: data.publicId,
      data_json: data.dataJson,
      solution_json: data.solutionJson,
      reject_reason: data.rejectReason,
      created_at: data.createdAt,
      updated_at: data.updatedAt,
    } as z.infer<typeof GetDraftDetailResponseApiSchema>;
  },
  toFrontend: (data: z.infer<typeof GetDraftDetailResponseApiSchema>): z.infer<typeof GetDraftDetailResponseAppSchema> => {
    // Transform API to frontend format with exercise type mapping
    const transformedExerciseType = transformExerciseType(data.exercise_type, data.data_json);

    return {
      id: data.id,
      publicId: data.public_id,
      exerciseType: transformedExerciseType,
      difficulty: data.difficulty,
      dataJson: data.data_json,
      solutionJson: data.solution_json,
      status: data.status,
      rejectReason: data.reject_reason,
      createdAt: data.created_at,
      updatedAt: data.updated_at,
    } as z.infer<typeof GetDraftDetailResponseAppSchema>;
  }
};

// Update Draft Request Schemas
const UpdateDraftRequestAppSchema = z.object({
  dataJson: z.record(z.any()).optional(),
  solutionJson: z.record(z.any()).optional(),
  difficulty: DifficultyEnum.optional(),
});

const UpdateDraftRequestApiSchema = z.object({
  data_json: z.record(z.any()).optional(),
  solution_json: z.record(z.any()).optional(),
  difficulty: DifficultyEnum.optional(),
});

export const UpdateDraftRequestSchema = createSchemaPair(
  UpdateDraftRequestAppSchema,
  UpdateDraftRequestApiSchema
);

// Update Draft Response Schemas
const UpdateDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: SimplifiedDraftDetailSchemaBase,
});

const UpdateDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: SimplifiedDraftDetailApiSchemaBase,
});

// Custom transformation for update draft response to handle exercise type mapping
export const UpdateDraftResponseSchema = {
  frontend: UpdateDraftResponseAppSchema,
  api: UpdateDraftResponseApiSchema,
  toApi: (data: z.infer<typeof UpdateDraftResponseAppSchema>): z.infer<typeof UpdateDraftResponseApiSchema> => {
    const reverseMapping: Record<string, string> = {
      'mc-simple': 'multiple_choice',
      'mc-multi': 'multiple_choice',
      'true-false': 'true_false',
      'error-correction': 'error_correction',
      'matching-pairs': 'matching_pairs',
    };

    return {
      success: data.success,
      message: data.message,
      draft: {
        ...data.draft,
        exercise_type: reverseMapping[data.draft.exerciseType] || data.draft.exerciseType,
        public_id: data.draft.publicId,
        data_json: data.draft.dataJson,
        solution_json: data.draft.solutionJson,
        reject_reason: data.draft.rejectReason,
        created_at: data.draft.createdAt,
        updated_at: data.draft.updatedAt,
      }
    } as z.infer<typeof UpdateDraftResponseApiSchema>;
  },
  toFrontend: (data: z.infer<typeof UpdateDraftResponseApiSchema>): z.infer<typeof UpdateDraftResponseAppSchema> => {
    const transformedExerciseType = transformExerciseType(data.draft.exercise_type, data.draft.data_json);

    return {
      success: data.success,
      message: data.message,
      draft: {
        id: data.draft.id,
        publicId: data.draft.public_id,
        exerciseType: transformedExerciseType,
        difficulty: data.draft.difficulty,
        dataJson: data.draft.data_json,
        solutionJson: data.draft.solution_json,
        status: data.draft.status,
        rejectReason: data.draft.reject_reason,
        createdAt: data.draft.created_at,
        updatedAt: data.draft.updated_at,
      }
    } as z.infer<typeof UpdateDraftResponseAppSchema>;
  }
};

// Accept Draft Request Schemas
const AcceptDraftRequestAppSchema = z.object({
});

const AcceptDraftRequestApiSchema = z.object({
});

export const AcceptDraftRequestSchema = createSchemaPair(
  AcceptDraftRequestAppSchema,
  AcceptDraftRequestApiSchema
);

// Accept Draft Response Schemas
const AcceptDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: z.object({
    id: z.number(),
    status: z.literal('ACCEPTED_BY_EDITOR'),
  }),
});

const AcceptDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: z.object({
    id: z.number(),
    status: z.literal('ACCEPTED_BY_EDITOR'),
  }),
});

export const AcceptDraftResponseSchema = createSchemaPair(
  AcceptDraftResponseAppSchema,
  AcceptDraftResponseApiSchema
);

// Create Draft Request Schemas
const CreateDraftRequestAppSchema = z.object({
  exerciseType: ExerciseTypeEnum,
  difficulty: DifficultyEnum,
  dataJson: z.record(z.any()),
  solutionJson: z.record(z.any()),
  learningNodeIds: z.array(z.string()),
});

const CreateDraftRequestApiSchema = z.object({
  exercise_type: ExerciseTypeEnum,
  difficulty: DifficultyEnum,
  data_json: z.record(z.any()),
  solution_json: z.record(z.any()),
  learning_node_ids: z.array(z.string()),
});

export const CreateDraftRequestSchema = createSchemaPair(
  CreateDraftRequestAppSchema,
  CreateDraftRequestApiSchema
);

// Minimal Draft Response Schema for Creation (only essential fields)
const MinimalDraftSchemaBase = z.object({
  id: z.number(),
  publicId: z.string(),
  status: DraftExerciseStatusEnum,
  exerciseType: ExerciseTypeEnum,
  difficulty: DifficultyEnum.optional(),
  dataJson: z.record(z.any()),
  solutionJson: z.record(z.any()).optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

const MinimalDraftApiSchemaBase = z.object({
  id: z.number(),
  public_id: z.string(),
  status: DraftExerciseStatusEnum,
  exercise_type: ExerciseTypeEnum,
  difficulty: DifficultyEnum.optional(),
  data_json: z.record(z.any()),
  solution_json: z.record(z.any()).optional(),
  created_at: z.string(),
  updated_at: z.string(),
});

// Create Draft Response Schemas
const CreateDraftResponseAppSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: MinimalDraftSchemaBase,
});

const CreateDraftResponseApiSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  draft: MinimalDraftApiSchemaBase,
});

// Custom transformation for create draft response to handle exercise type mapping
export const CreateDraftResponseSchema = {
  frontend: CreateDraftResponseAppSchema,
  api: CreateDraftResponseApiSchema,
  toApi: (data: z.infer<typeof CreateDraftResponseAppSchema>): z.infer<typeof CreateDraftResponseApiSchema> => {
    const reverseMapping: Record<string, string> = {
      'mc-simple': 'multiple_choice',
      'mc-multi': 'multiple_choice',
      'true-false': 'true_false',
      'error-correction': 'error_correction',
      'matching-pairs': 'matching_pairs',
    };

    return {
      success: data.success,
      message: data.message,
      draft: {
        ...data.draft,
        exercise_type: reverseMapping[data.draft.exerciseType] || data.draft.exerciseType,
        public_id: data.draft.publicId,
        data_json: data.draft.dataJson,
        solution_json: data.draft.solutionJson,
        created_at: data.draft.createdAt,
        updated_at: data.draft.updatedAt,
      }
    } as z.infer<typeof CreateDraftResponseApiSchema>;
  },
  toFrontend: (data: z.infer<typeof CreateDraftResponseApiSchema>): z.infer<typeof CreateDraftResponseAppSchema> => {
    const transformedExerciseType = transformExerciseType(data.draft.exercise_type, data.draft.data_json);

    return {
      success: data.success,
      message: data.message,
      draft: {
        id: data.draft.id,
        publicId: data.draft.public_id,
        exerciseType: transformedExerciseType,
        difficulty: data.draft.difficulty,
        dataJson: data.draft.data_json,
        solutionJson: data.draft.solution_json,
        status: data.draft.status,
        createdAt: data.draft.created_at,
        updatedAt: data.draft.updated_at,
      }
    } as z.infer<typeof CreateDraftResponseAppSchema>;
  }
};

// Export types
export type GetDraftsRequestApp = z.infer<typeof GetDraftsRequestSchema.frontend>;
export type GetDraftsResponseApp = z.infer<typeof GetDraftsResponseSchema.frontend>;
export type DraftListItemApp = z.infer<typeof DraftListItemSchemaBase>;
export type GetDraftDetailResponseApp = z.infer<typeof GetDraftDetailResponseSchema.frontend>;
export type UpdateDraftRequestApp = z.infer<typeof UpdateDraftRequestSchema.frontend>;
export type UpdateDraftResponseApp = z.infer<typeof UpdateDraftResponseSchema.frontend>;
export type AcceptDraftRequestApp = z.infer<typeof AcceptDraftRequestSchema.frontend>;
export type AcceptDraftResponseApp = z.infer<typeof AcceptDraftResponseSchema.frontend>;
export type CreateDraftRequestApp = z.infer<typeof CreateDraftRequestSchema.frontend>;
export type CreateDraftResponseApp = z.infer<typeof CreateDraftResponseSchema.frontend>;

// Utility functions for validating draft data
export function validateDraftData(exerciseType: z.infer<typeof ExerciseTypeEnum>, dataJson: any, solutionJson?: any) {
  // Validate exercise data
  const validatedData = validateExerciseData(exerciseType as keyof typeof exerciseDataSchemaMap, dataJson);

  // Validate solution if provided
  let validatedSolution;
  if (solutionJson) {
    validatedSolution = validateExerciseSolution(solutionJson);
  }

  return {
    dataJson: validatedData,
    solutionJson: validatedSolution,
  };
}

export function isDraftDataValid(exerciseType: z.infer<typeof ExerciseTypeEnum>, dataJson: any, solutionJson?: any): boolean {
  try {
    validateDraftData(exerciseType, dataJson, solutionJson);
    return true;
  } catch {
    return false;
  }
}