'use client';

import { useEffect, useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { useInternalAuthStore, selectIsInternalAuthenticated } from '@/zustand/internal/internalAuthStore/internalAuthStore';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import StatusBadge from '@/components/internal/StatusBadge';
import {
  Plus,
  FileText,
  Calendar,
  ChevronLeft,
  ChevronRight,
  BookOpen,
  XCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react';
import { useErrorHandler } from '@/utils/internal/errorHandling';
import { getDrafts } from '@/services/internal/draftService';
import { getLearningNodeDetail, getLearningNodeContent } from '@/services/internal/editorService';
import { DraftListItemApp } from '@/schemas/internal/draftSchema';
import { LearningNodeDetailApp, LearningNodeContentApp } from '@/schemas/internal/editorSchema';
import { formatDateByLocale } from '@/utils/dateFormatting';
import LearningNodeContentDisplay from '@/components/internal/LearningNodeContentDisplay';

export default function LearningNodeDetailPage() {
  const router = useRouter();
  const params = useParams();
  const learningNodeId = params.learningNodeId as string;
  const { handleError } = useErrorHandler();

  // Auth state
  const { initialized, isLoading: authLoading } = useInternalAuthStore();
  const isAuthenticated = useInternalAuthStore(selectIsInternalAuthenticated);

  const [drafts, setDrafts] = useState<DraftListItemApp[]>([]);
  const [totalDrafts, setTotalDrafts] = useState<number>(0);
  
  // Calculate draft counts from actual drafts
  const newDraftsCount = drafts.filter(d => d.status === 'NEW').length;
  const inReviewDraftsCount = drafts.filter(d => d.status === 'IN_REVIEW').length;
  const [isLoading, setIsLoading] = useState(true);
  const [learningNodeInfo, setLearningNodeInfo] = useState<LearningNodeDetailApp | null>(null);
  const [learningNodeContent, setLearningNodeContent] = useState<LearningNodeContentApp | null>(null);
  const [contentLoading, setContentLoading] = useState(false);
  const [contentError, setContentError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Load learning node details and drafts
  useEffect(() => {
    if (!initialized || authLoading || !isAuthenticated || !learningNodeId) {
      return;
    }

    loadData();
  }, [initialized, authLoading, isAuthenticated, learningNodeId]);

  // Load learning node content when component mounts (after basic data is loaded)
  useEffect(() => {
    if (learningNodeInfo && !contentLoading && !learningNodeContent && !contentError) {
      loadLearningNodeContent();
    }
  }, [learningNodeInfo]);

  const loadLearningNodeContent = async () => {
    try {
      setContentLoading(true);
      setContentError(null);

      const contentResult = await getLearningNodeContent(learningNodeId);
      if (contentResult.status === 'success') {
        setLearningNodeContent(contentResult.data);
      } else {
        setContentError(`Failed to load learning node content: ${contentResult.message}`);
        handleError(contentResult.message, 'Loading learning node content');
      }
    } catch (error) {
      const errorMessage = 'Failed to load learning node content';
      setContentError(errorMessage);
      handleError(error, 'Loading learning node content');
    } finally {
      setContentLoading(false);
    }
  };

  const loadData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load learning node details first
      const nodeResult = await getLearningNodeDetail(learningNodeId);
      if (nodeResult.status === 'success') {
        setLearningNodeInfo(nodeResult.data);
      } else {
        setError(`Failed to load learning node: ${nodeResult.message}`);
        handleError(nodeResult.message, 'Loading learning node details');
        return; // Don't continue if we can't load the learning node
      }

      // Load drafts for this learning node
      // Use maximum allowed page size
      const draftsResult = await getDrafts({ page: 1, pageSize: 100 });
      if (draftsResult.status === 'success') {
        // Filter drafts that belong to this learning node
        const filteredDrafts = draftsResult.data.drafts.filter(draft =>
          draft.learningNode && draft.learningNode.publicId === learningNodeId
        );
        setDrafts(filteredDrafts);
        setTotalDrafts(filteredDrafts.length);
        
        // Note: If there are more than 100 total drafts, we might not get all drafts for this node
        // This is acceptable since most learning nodes won't have that many drafts
      } else {
        handleError(draftsResult.message, 'Loading drafts');
      }
    } catch (error) {
      const errorMessage = 'Failed to load learning node data';
      setError(errorMessage);
      handleError(error, 'Loading data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateDraft = () => {
    router.push(`/internal/editor/${learningNodeId}/new`);
  };

  const renderDraftCard = (draft: DraftListItemApp) => (
    <Card 
      key={draft.id} 
      className="hover:shadow-md hover:scale-[1.005] transition-all cursor-pointer group"
      onClick={() => router.push(`/internal/editor/${learningNodeId}/${draft.publicId}`)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <StatusBadge status={draft.status} />
              {draft.difficulty && (
                <Badge variant="secondary" className="text-xs">
                  {draft.difficulty}
                </Badge>
              )}
            </div>

            <h3 className="font-medium text-gray-900 mb-1">
              Exercise Draft
            </h3>
            
            <div className="flex items-center gap-4 text-xs text-gray-500">
              {draft.updatedAt && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  Updated {formatDateByLocale(draft.updatedAt, 'en', 'PPp')}
                </div>
              )}
              {draft.mediaFiles && draft.mediaFiles.length > 0 && (
                <span>{draft.mediaFiles.length} media files</span>
              )}
            </div>
            
            {(draft.status === 'REJECTED_BY_ADMIN' || draft.status === 'REJECTED_BY_EDITOR') && draft.rejectReason && (
              <div className={`mt-3 p-4 ${draft.status === 'REJECTED_BY_ADMIN' ? 'bg-red-50 dark:bg-red-900/20 border-red-300 dark:border-red-700' : 'bg-orange-50 dark:bg-orange-900/20 border-orange-300 dark:border-orange-700'} border-2 rounded-lg`}>
                <div className="flex items-start gap-3">
                  <XCircle className={`h-5 w-5 mt-0.5 flex-shrink-0 ${draft.status === 'REJECTED_BY_ADMIN' ? 'text-red-600 dark:text-red-400' : 'text-orange-600 dark:text-orange-400'}`} />
                  <div className="flex-1">
                    <h4 className={`font-semibold ${draft.status === 'REJECTED_BY_ADMIN' ? 'text-red-900 dark:text-red-300' : 'text-orange-900 dark:text-orange-300'} mb-1`}>
                      Action Required: {draft.status === 'REJECTED_BY_ADMIN' ? 'Admin' : 'Editor'} Feedback
                    </h4>
                    <p className={draft.status === 'REJECTED_BY_ADMIN' ? 'text-red-800 dark:text-red-200' : 'text-orange-800 dark:text-orange-200'}>{draft.rejectReason}</p>
                    <p className={`text-sm ${draft.status === 'REJECTED_BY_ADMIN' ? 'text-red-700 dark:text-red-300' : 'text-orange-700 dark:text-orange-300'} mt-2 font-medium`}>
                      Please address this feedback and resubmit your draft.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <div className="flex items-center">
            <ChevronRight className="h-5 w-5 text-gray-400 transition-all duration-200 group-hover:translate-x-1 group-hover:text-gray-600 dark:group-hover:text-gray-300" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/internal/editor')}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="h-4 w-4" />
            Back to Content Assignments
          </Button>
        </div>

        <Card>
          <CardContent className="p-12 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading Learning Node</h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={loadData} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with back button */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/internal/editor')}
          className="flex items-center gap-2"
        >
          <ChevronLeft className="h-4 w-4" />
          Back to Content Assignments
        </Button>
      </div>

      {/* Learning Node Info */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {learningNodeInfo ? `Learning Node: ${learningNodeInfo.title}` : 'Loading...'}
          </h1>
          {learningNodeInfo && (
            <div className="mt-2 space-y-1">
              <p className="text-gray-600">
                {learningNodeInfo.chapter.subject.name} • {learningNodeInfo.chapter.title}
              </p>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span>{learningNodeInfo.exercisesCount} published exercises</span>
                <span>•</span>
                <span>{totalDrafts} drafts</span>
                {newDraftsCount > 0 && (
                  <>
                    <span>•</span>
                    <span className="text-green-600 font-medium">{newDraftsCount} new</span>
                  </>
                )}
                {inReviewDraftsCount > 0 && (
                  <>
                    <span>•</span>
                    <span className="text-amber-600 font-medium">{inReviewDraftsCount} in review</span>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
        <Button
          onClick={handleCreateDraft}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Create New Draft
        </Button>
      </div>

      {/* Tabs for Content and Drafts */}
      <Tabs defaultValue="content" className="space-y-4">
        <TabsList>
          <TabsTrigger value="content">
            <BookOpen className="h-4 w-4 mr-2" />
            Content
          </TabsTrigger>
          <TabsTrigger value="drafts">
            <FileText className="h-4 w-4 mr-2" />
            Exercise Drafts ({drafts.length})
            {newDraftsCount > 0 && (
              <Badge variant="default" className="ml-2 bg-green-600 text-white border-green-600 hover:bg-green-700 text-xs px-1.5 py-0">
                {newDraftsCount} new
              </Badge>
            )}
            {inReviewDraftsCount > 0 && (
              <Badge variant="default" className="ml-2 bg-amber-600 text-white border-amber-600 hover:bg-amber-700 text-xs px-1.5 py-0">
                {inReviewDraftsCount} in review
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="content" className="space-y-4">
          <LearningNodeContentDisplay
            learningNode={learningNodeContent}
            isLoading={contentLoading}
            error={contentError}
            onRetry={loadLearningNodeContent}
          />
        </TabsContent>

        <TabsContent value="drafts" className="space-y-4">
          {isLoading ? (
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <Card key={i}>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <Skeleton className="h-6 w-20" />
                        <Skeleton className="h-6 w-24" />
                        <Skeleton className="h-6 w-16" />
                      </div>
                      <Skeleton className="h-5 w-2/3" />
                      <Skeleton className="h-4 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : drafts.length > 0 ? (
            <div className="space-y-4">
              {drafts.map(renderDraftCard)}
            </div>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No drafts created yet for this learning node</p>
                <p className="text-sm text-gray-500 mt-2 mb-4">
                  Get started by creating your first exercise draft
                </p>
                <Button onClick={handleCreateDraft}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Draft
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}