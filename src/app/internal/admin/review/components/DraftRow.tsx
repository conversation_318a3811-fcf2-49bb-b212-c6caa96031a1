import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { User, Calendar, FileText, CheckCircle, XCircle, Trash2, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDateByLocale } from '@/utils/dateFormatting';
import { DraftRowProps } from '../types';
import { getDraftTitle } from '../utils/draftHelpers';
import StatusBadge from '@/components/internal/StatusBadge';

export function DraftRow({
  draft,
  isSelected,
  onSelect,
  onReview,
  onPublish,
  onReject,
  onDelete,
  isPublishing,
  isRejecting,
  isDeleting,
}: DraftRowProps) {
  const isAnyActionInProgress = isPublishing || isRejecting || isDeleting;

  return (
    <div
      className={cn(
        "flex items-center gap-4 p-3 border rounded transition-colors",
        isSelected ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
      )}
    >
      <Checkbox
        checked={isSelected}
        onCheckedChange={(checked) => onSelect(checked as boolean)}
      />
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <span className="font-medium text-sm">
            {getDraftTitle(draft)}
          </span>
          <StatusBadge status={draft.status} showText={true} />
          {draft.difficulty && (
            <Badge variant="outline" className="text-xs">
              {draft.difficulty}
            </Badge>
          )}
        </div>
        
        <div className="text-xs text-gray-600 space-y-1">
          {draft.learningNode && (
            <div>{draft.learningNode.title}</div>
          )}
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <User className="h-3 w-3" />
              {draft.assignedEditor || 'Unassigned'}
            </span>
            <span className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              Updated {draft.updatedAt ? formatDateByLocale(draft.updatedAt, 'en', 'PPp') : 'N/A'}
            </span>
          </div>
        </div>
        
        {/* Show rejection reason for editor-rejected drafts */}
        {draft.status === 'REJECTED_BY_EDITOR' && draft.rejectReason && (
          <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded-md">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs">
                <span className="font-semibold text-orange-900">Editor Rejection Reason:</span>
                <p className="text-orange-800 mt-0.5">{draft.rejectReason}</p>
              </div>
            </div>
          </div>
        )}
      </div>
      
      <div className="flex items-center gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={onReview}
        >
          <FileText className="h-3 w-3 mr-1" />
          Review
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={onPublish}
          disabled={isPublishing}
          className="text-green-600 hover:text-green-700"
        >
          <CheckCircle className="h-3 w-3 mr-1" />
          Publish
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={onReject}
          disabled={isAnyActionInProgress}
          className="text-red-600 hover:text-red-700"
        >
          <XCircle className="h-3 w-3 mr-1" />
          Reject
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={onDelete}
          disabled={isAnyActionInProgress}
          className="text-red-700 hover:text-red-800 border-red-200 hover:border-red-300"
        >
          <Trash2 className="h-3 w-3 mr-1" />
          Delete
        </Button>
      </div>
    </div>
  );
}