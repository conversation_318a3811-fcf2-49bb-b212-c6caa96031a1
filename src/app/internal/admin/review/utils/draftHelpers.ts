import { DraftListItem } from '@/types/internal/editorial';
import { formatExerciseType } from '@/utils/internal/exerciseTypeLabels';

export function getDraftTitle(draft: DraftListItem): string {
  return `${formatExerciseType(draft.exerciseType)} Exercise`;
}

export function canNavigateToDraft(draft: DraftListItem): boolean {
  return draft.learningNode !== undefined;
}

export function getDraftLearningNodeId(draft: DraftListItem): string | null {
  if (draft.learningNode) {
    return draft.learningNode.publicId;
  }
  return null;
}

export function formatDraftCount(count: number): string {
  return `${count} draft${count !== 1 ? 's' : ''}`;
}

export function calculateTotalDrafts(chapters: Record<string, DraftListItem[]>): number {
  return Object.values(chapters).reduce((total, chapterDrafts) => total + chapterDrafts.length, 0);
}