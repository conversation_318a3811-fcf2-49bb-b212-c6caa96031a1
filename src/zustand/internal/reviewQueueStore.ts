import { create } from 'zustand';
import { getDrafts } from '@/services/internal/draftService';
import { publishDraft, rejectDraft, deleteDraft, bulkPublish } from '@/services/internal/adminService';
import { DraftListItem } from '@/types/internal/editorial';
import { PAGINATION_CONFIG } from '@/config/constants';

interface GroupedDrafts {
  [subjectName: string]: {
    [chapterTitle: string]: DraftListItem[];
  };
}

interface ReviewQueueState {
  drafts: DraftListItem[];
  groupedDrafts: GroupedDrafts;
  selectedDraftIds: number[];
  filters: {
    subjectId?: number;
    editorId?: number;
    dateFrom?: string;
    dateTo?: string;
  };
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  isLoading: boolean;
  isPublishing: boolean;
  isRejecting: boolean;
  isDeleting: boolean;
  isBulkPublishing: boolean;
  error: string | null;
  lastRefresh: string | null;
}

interface ReviewQueueActions {
  fetchDrafts: () => Promise<void>;
  setFilters: (filters: Partial<ReviewQueueState['filters']>) => void;
  setPage: (page: number) => void;
  setPageSize: (pageSize: number) => void;
  selectDraft: (draftId: number) => void;
  deselectDraft: (draftId: number) => void;
  selectAll: (subjectName?: string, chapterTitle?: string) => void;
  deselectAll: () => void;
  publishSingleDraft: (draftPublicId: string, publishNotes?: string) => Promise<boolean>;
  rejectSingleDraft: (draftId: number, reason: string) => Promise<boolean>;
  deleteSingleDraft: (draftId: number) => Promise<boolean>;
  bulkPublishSelected: (publishNotes?: string) => Promise<boolean>;
  refresh: () => Promise<void>;
  reset: () => void;
  
  // Internal setters
  _setDrafts: (drafts: DraftListItem[]) => void;
  _setLoading: (isLoading: boolean) => void;
  _setError: (error: string | null) => void;
  _setSelectedDraftIds: (ids: number[]) => void;
}

export type ReviewQueueStore = ReviewQueueState & ReviewQueueActions;

const initialState: ReviewQueueState = {
  drafts: [],
  groupedDrafts: {},
  selectedDraftIds: [],
  filters: {},
  pagination: {
    page: 1,
    pageSize: PAGINATION_CONFIG.REVIEW_QUEUE_PAGE_SIZE,
    total: 0,
    totalPages: 0,
  },
  isLoading: false,
  isPublishing: false,
  isRejecting: false,
  isDeleting: false,
  isBulkPublishing: false,
  error: null,
  lastRefresh: null,
};

export const useReviewQueueStore = create<ReviewQueueStore>((set, get) => ({
  ...initialState,

  fetchDrafts: async () => {
    set({ isLoading: true, error: null });

    try {
      const { filters, pagination } = get();
      
      // Fetch both accepted and rejected drafts in parallel
      const [acceptedResult, rejectedResult] = await Promise.all([
        getDrafts({
          status: 'ACCEPTED_BY_EDITOR',
          subjectId: filters.subjectId,
          editorId: filters.editorId,
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo,
          page: pagination.page,
          pageSize: Math.floor(pagination.pageSize / 2), // Split page size between the two requests
        }),
        getDrafts({
          status: 'REJECTED_BY_EDITOR',
          subjectId: filters.subjectId,
          editorId: filters.editorId,
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo,
          page: pagination.page,
          pageSize: Math.floor(pagination.pageSize / 2),
        })
      ]);

      if (acceptedResult.status === 'success' && rejectedResult.status === 'success') {
        // Combine drafts from both results
        const acceptedDrafts = acceptedResult.data.drafts || [];
        const rejectedDrafts = rejectedResult.data.drafts || [];
        const allDrafts = [...acceptedDrafts, ...rejectedDrafts];
        
        const groupedDrafts = groupDraftsBySubjectAndChapter(allDrafts);
        
        // Calculate combined pagination
        const acceptedPagination = acceptedResult.data.pagination || {
          totalItems: acceptedDrafts.length,
          totalPages: 1,
        };
        const rejectedPagination = rejectedResult.data.pagination || {
          totalItems: rejectedDrafts.length,
          totalPages: 1,
        };
        
        const totalItems = acceptedPagination.totalItems + rejectedPagination.totalItems;
        const totalPages = Math.max(acceptedPagination.totalPages, rejectedPagination.totalPages);
        
        set({
          drafts: allDrafts,
          groupedDrafts,
          pagination: {
            ...pagination,
            total: totalItems,
            totalPages: totalPages,
          },
          isLoading: false,
          lastRefresh: new Date().toISOString(),
        });
      } else {
        const errorMessage = acceptedResult.status !== 'success' 
          ? acceptedResult.message 
          : rejectedResult.message;
        throw new Error(errorMessage || 'Failed to fetch drafts');
      }
    } catch (error) {
      console.error('Failed to fetch drafts:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch drafts',
        isLoading: false,
      });
    }
  },

  setFilters: (newFilters) => {
    set(state => ({
      filters: { ...state.filters, ...newFilters },
      pagination: { ...state.pagination, page: 1 }, // Reset to first page
    }));
    // Auto-fetch when filters change
    get().fetchDrafts();
  },

  setPage: (page) => {
    set(state => ({
      pagination: { ...state.pagination, page }
    }));
    get().fetchDrafts();
  },

  setPageSize: (pageSize) => {
    set(state => ({
      pagination: { ...state.pagination, pageSize, page: 1 }
    }));
    get().fetchDrafts();
  },

  selectDraft: (draftId) => {
    set(state => ({
      selectedDraftIds: state.selectedDraftIds.includes(draftId)
        ? state.selectedDraftIds
        : [...state.selectedDraftIds, draftId]
    }));
  },

  deselectDraft: (draftId) => {
    set(state => ({
      selectedDraftIds: state.selectedDraftIds.filter(id => id !== draftId)
    }));
  },

  selectAll: (subjectName, chapterTitle) => {
    const { drafts, groupedDrafts } = get();
    
    let draftsToSelect: DraftListItem[] = [];
    
    if (subjectName && chapterTitle) {
      // Select all in specific chapter
      draftsToSelect = groupedDrafts[subjectName]?.[chapterTitle] || [];
    } else if (subjectName) {
      // Select all in subject
      const subjectChapters = groupedDrafts[subjectName] || {};
      draftsToSelect = Object.values(subjectChapters).flat();
    } else {
      // Select all drafts
      draftsToSelect = drafts;
    }
    
    const newSelectedIds = Array.from(new Set([
      ...get().selectedDraftIds,
      ...draftsToSelect.map(draft => draft.id)
    ]));
    
    set({ selectedDraftIds: newSelectedIds });
  },

  deselectAll: () => {
    set({ selectedDraftIds: [] });
  },

  publishSingleDraft: async (draftPublicId, publishNotes) => {
    set({ isPublishing: true, error: null });

    try {
      const result = await publishDraft(draftPublicId, { publishNotes });

      if (result.status === 'success') {
        // Remove published draft from the list by finding it by publicId
        set(state => ({
          drafts: state.drafts.filter(draft => draft.publicId !== draftPublicId),
          selectedDraftIds: state.selectedDraftIds.filter(id => {
            // Find the draft with this publicId and remove its internal id from selection
            const draft = state.drafts.find(d => d.publicId === draftPublicId);
            return draft ? id !== draft.id : true;
          }),
          isPublishing: false,
        }));

        // Refresh grouped drafts
        const { drafts } = get();
        const groupedDrafts = groupDraftsBySubjectAndChapter(drafts);
        set({ groupedDrafts });

        return true;
      } else {
        throw new Error(result.message || 'Failed to publish draft');
      }
    } catch (error) {
      console.error('Failed to publish draft:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to publish draft',
        isPublishing: false,
      });
      return false;
    }
  },

  rejectSingleDraft: async (draftId, reason) => {
    set({ isRejecting: true, error: null });

    try {
      const result = await rejectDraft(draftId, { rejectionReason: reason });

      if (result.status === 'success') {
        // Remove rejected draft from the list
        set(state => ({
          drafts: state.drafts.filter(draft => draft.id !== draftId),
          selectedDraftIds: state.selectedDraftIds.filter(id => id !== draftId),
          isRejecting: false,
        }));

        // Refresh grouped drafts
        const { drafts } = get();
        const groupedDrafts = groupDraftsBySubjectAndChapter(drafts);
        set({ groupedDrafts });

        return true;
      } else {
        throw new Error(result.message || 'Failed to reject draft');
      }
    } catch (error) {
      console.error('Failed to reject draft:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to reject draft',
        isRejecting: false,
      });
      return false;
    }
  },

  deleteSingleDraft: async (draftId) => {
    set({ isDeleting: true, error: null });

    try {
      const result = await deleteDraft(draftId);

      if (result.status === 'success') {
        // Remove deleted draft from the list
        set(state => ({
          drafts: state.drafts.filter(draft => draft.id !== draftId),
          selectedDraftIds: state.selectedDraftIds.filter(id => id !== draftId),
          isDeleting: false,
        }));

        // Refresh grouped drafts
        const { drafts } = get();
        const groupedDrafts = groupDraftsBySubjectAndChapter(drafts);
        set({ groupedDrafts });

        return true;
      } else {
        throw new Error(result.message || 'Failed to delete draft');
      }
    } catch (error) {
      console.error('Failed to delete draft:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to delete draft',
        isDeleting: false,
      });
      return false;
    }
  },

  bulkPublishSelected: async (publishNotes) => {
    const { selectedDraftIds } = get();
    
    if (selectedDraftIds.length === 0) {
      return false;
    }

    set({ isBulkPublishing: true, error: null });

    try {
      const result = await bulkPublish({
        draftIds: selectedDraftIds,
        batchName: `Bulk publish ${selectedDraftIds.length} drafts`,
        batchNotes: publishNotes,
      });

      if (result.status === 'success') {
        // Remove published drafts from the list
        set(state => ({
          drafts: state.drafts.filter(draft => !selectedDraftIds.includes(draft.id)),
          selectedDraftIds: [],
          isBulkPublishing: false,
        }));
        
        // Refresh grouped drafts
        const { drafts } = get();
        const groupedDrafts = groupDraftsBySubjectAndChapter(drafts);
        set({ groupedDrafts });
        
        return true;
      } else {
        throw new Error(result.message || 'Failed to bulk publish drafts');
      }
    } catch (error) {
      console.error('Failed to bulk publish drafts:', error);
      set({
        error: error instanceof Error ? error.message : 'Failed to bulk publish drafts',
        isBulkPublishing: false,
      });
      return false;
    }
  },

  refresh: () => {
    return get().fetchDrafts();
  },

  reset: () => {
    set(initialState);
  },

  // Internal setters
  _setDrafts: (drafts) => {
    const groupedDrafts = groupDraftsBySubjectAndChapter(drafts);
    set({ drafts, groupedDrafts });
  },
  _setLoading: (isLoading) => set({ isLoading }),
  _setError: (error) => set({ error }),
  _setSelectedDraftIds: (selectedDraftIds) => set({ selectedDraftIds }),
}));

// Helper function to group drafts by subject and chapter
function groupDraftsBySubjectAndChapter(drafts: DraftListItem[]): GroupedDrafts {
  const grouped: GroupedDrafts = {};

  drafts.forEach(draft => {
    if (draft.learningNode) {
      const node = draft.learningNode;
      const subjectName = node.chapter.subject.name;
      const chapterTitle = node.chapter.title;

      if (!grouped[subjectName]) {
        grouped[subjectName] = {};
      }
      if (!grouped[subjectName][chapterTitle]) {
        grouped[subjectName][chapterTitle] = [];
      }

      grouped[subjectName][chapterTitle].push(draft);
    }
  });

  return grouped;
}

// Selector helpers
export const selectDrafts = (state: ReviewQueueStore) => state.drafts;
export const selectGroupedDrafts = (state: ReviewQueueStore) => state.groupedDrafts;
export const selectSelectedDraftIds = (state: ReviewQueueStore) => state.selectedDraftIds;
export const selectFilters = (state: ReviewQueueStore) => state.filters;
export const selectPagination = (state: ReviewQueueStore) => state.pagination;
export const selectIsLoading = (state: ReviewQueueStore) => state.isLoading;
export const selectError = (state: ReviewQueueStore) => state.error;
export const selectIsPublishing = (state: ReviewQueueStore) => state.isPublishing;
export const selectIsRejecting = (state: ReviewQueueStore) => state.isRejecting;
export const selectIsDeleting = (state: ReviewQueueStore) => state.isDeleting;
export const selectIsBulkPublishing = (state: ReviewQueueStore) => state.isBulkPublishing;
export const selectSelectedCount = (state: ReviewQueueStore) => state.selectedDraftIds.length;