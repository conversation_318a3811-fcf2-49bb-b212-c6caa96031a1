# 🧹 Draft Exercise Cleanup - NEW Status Only

## 🎯 **Updated Strategy**

The cleanup has been modified to **only delete draft exercises with `NEW` status**, making it much safer by preserving:
- ✅ Drafts `IN_REVIEW` (being worked on)
- ✅ Drafts `ACCEPTED_BY_EDITOR` (approved work)
- ✅ Drafts `REJECTED_BY_EDITOR` (feedback provided)
- ✅ Drafts `REJECTED_BY_ADMIN` (admin decisions)
- ✅ Drafts `PUBLISHED` (completed work)

## 📋 **Quick Start Commands**

### 1. Check Current Status Distribution
```sql
SELECT 
    status,
    COUNT(*) as count,
    ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER(), 2) as percentage
FROM draft_exercise
GROUP BY status
ORDER BY count DESC;
```

### 2. Preview What Will Be Deleted
```sql
WITH ranked_new_drafts AS (
    SELECT 
        de.id,
        de.learning_node_id,
        ln.title as learning_node_title,
        ROW_NUMBER() OVER (
            PARTITION BY de.learning_node_id 
            ORDER BY de.created_at DESC, de.id DESC
        ) as rank_newest_first
    FROM draft_exercise de
    JOIN learning_node ln ON de.learning_node_id = ln.id
    WHERE de.status = 'NEW'
)
SELECT 
    learning_node_title,
    COUNT(*) as new_drafts_to_delete
FROM ranked_new_drafts
WHERE rank_newest_first > 24
GROUP BY learning_node_id, learning_node_title
ORDER BY new_drafts_to_delete DESC;
```

### 3. Execute Cleanup (After Review)
```sql
BEGIN;

WITH ranked_new_drafts AS (
    SELECT 
        id,
        ROW_NUMBER() OVER (
            PARTITION BY learning_node_id 
            ORDER BY created_at DESC, id DESC
        ) as rank_newest_first
    FROM draft_exercise
    WHERE status = 'NEW'
)
DELETE FROM draft_exercise
WHERE id IN (
    SELECT id 
    FROM ranked_new_drafts 
    WHERE rank_newest_first > 24
);

-- Verify results
SELECT 
    'Cleanup completed' as status,
    COUNT(*) as remaining_total_drafts,
    COUNT(CASE WHEN status = 'NEW' THEN 1 END) as remaining_new_drafts
FROM draft_exercise;

COMMIT;
```

## 🛡️ **Safety Features**

1. **Status Filter**: Only affects `NEW` drafts (untouched work)
2. **Preserves Work**: Keeps all drafts with editor/admin interaction
3. **Transaction Safe**: Wrapped in BEGIN/COMMIT for rollback
4. **Preview First**: Always shows impact before execution
5. **Cascade Safe**: Automatically handles related media files

## 📊 **What Gets Deleted**

- ✅ **NEW drafts ranked 25+ per learning node** (oldest NEW drafts)
- ❌ **All other statuses preserved** (work in progress/completed)
- ❌ **Learning nodes with ≤24 NEW drafts** (no deletion)

## 🎯 **Expected Impact**

This approach is much more conservative and will likely delete fewer records since:
- Many excess drafts may already be `IN_REVIEW` or other statuses
- Only truly unused `NEW` drafts are removed
- All valuable work and feedback is preserved

Run the preview queries first to see the actual impact in your database!
