-- ============================================================================
-- DRAFT EXERCISE CLEANUP: Limit to 24 drafts per learning node
-- ============================================================================
-- This script removes excess draft exercises, keeping only the 24 most recent
-- ones for each learning node (based on created_at timestamp, with id as tiebreaker)
--
-- IMPORTANT: Run the PREVIEW query first to see what will be deleted!
-- ============================================================================

-- ============================================================================
-- STEP 1: PREVIEW - See what will be deleted (RUN THIS FIRST!)
-- ============================================================================

-- Preview: Show learning nodes with more than 24 drafts and what will be deleted
WITH ranked_drafts AS (
    SELECT 
        de.id,
        de.public_id,
        de.learning_node_id,
        de.exercise_type,
        de.status,
        de.created_at,
        ln.title as learning_node_title,
        ln.public_id as learning_node_public_id,
        ROW_NUMBER() OVER (
            PARTITION BY de.learning_node_id 
            ORDER BY de.created_at DESC, de.id DESC
        ) as rank_newest_first
    FROM draft_exercise de
    JOIN learning_node ln ON de.learning_node_id = ln.id
),
drafts_to_delete AS (
    SELECT *
    FROM ranked_drafts
    WHERE rank_newest_first > 24
)
SELECT 
    'PREVIEW: Drafts that will be DELETED' as action,
    learning_node_public_id,
    learning_node_title,
    COUNT(*) as drafts_to_delete,
    MIN(created_at) as oldest_draft_to_delete,
    MAX(created_at) as newest_draft_to_delete,
    STRING_AGG(
        DISTINCT status::text, 
        ', ' 
        ORDER BY status::text
    ) as statuses_to_delete
FROM drafts_to_delete
GROUP BY learning_node_public_id, learning_node_title
ORDER BY drafts_to_delete DESC;

-- Preview: Show detailed list of drafts that will be deleted
WITH ranked_drafts AS (
    SELECT 
        de.id,
        de.public_id,
        de.learning_node_id,
        de.exercise_type,
        de.status,
        de.created_at,
        ln.title as learning_node_title,
        ln.public_id as learning_node_public_id,
        ROW_NUMBER() OVER (
            PARTITION BY de.learning_node_id 
            ORDER BY de.created_at DESC, de.id DESC
        ) as rank_newest_first
    FROM draft_exercise de
    JOIN learning_node ln ON de.learning_node_id = ln.id
)
SELECT 
    'DETAILED PREVIEW: Individual drafts to delete' as action,
    public_id as draft_public_id,
    exercise_type,
    status,
    created_at,
    learning_node_public_id,
    learning_node_title,
    rank_newest_first
FROM ranked_drafts
WHERE rank_newest_first > 24
ORDER BY learning_node_public_id, rank_newest_first;

-- Preview: Summary statistics
WITH ranked_drafts AS (
    SELECT 
        de.learning_node_id,
        ROW_NUMBER() OVER (
            PARTITION BY de.learning_node_id 
            ORDER BY de.created_at DESC, de.id DESC
        ) as rank_newest_first
    FROM draft_exercise de
)
SELECT 
    'SUMMARY STATISTICS' as info,
    COUNT(DISTINCT learning_node_id) as total_learning_nodes,
    COUNT(*) as total_drafts,
    COUNT(CASE WHEN rank_newest_first <= 24 THEN 1 END) as drafts_to_keep,
    COUNT(CASE WHEN rank_newest_first > 24 THEN 1 END) as drafts_to_delete,
    ROUND(
        100.0 * COUNT(CASE WHEN rank_newest_first > 24 THEN 1 END) / COUNT(*), 
        2
    ) as percent_to_delete
FROM ranked_drafts;

-- ============================================================================
-- STEP 2: ACTUAL DELETION (Run only after reviewing preview!)
-- ============================================================================

-- TRANSACTION WRAPPER - Uncomment to run the actual deletion
/*
BEGIN;

-- Create a backup table first (optional but recommended)
CREATE TABLE IF NOT EXISTS draft_exercise_cleanup_backup_$(date +%Y%m%d) AS
SELECT de.*, ln.title as learning_node_title, ln.public_id as learning_node_public_id
FROM draft_exercise de
JOIN learning_node ln ON de.learning_node_id = ln.id
WHERE de.id IN (
    WITH ranked_drafts AS (
        SELECT 
            id,
            ROW_NUMBER() OVER (
                PARTITION BY learning_node_id 
                ORDER BY created_at DESC, id DESC
            ) as rank_newest_first
        FROM draft_exercise
    )
    SELECT id
    FROM ranked_drafts
    WHERE rank_newest_first > 24
);

-- Perform the actual deletion
WITH ranked_drafts AS (
    SELECT 
        id,
        learning_node_id,
        ROW_NUMBER() OVER (
            PARTITION BY learning_node_id 
            ORDER BY created_at DESC, id DESC
        ) as rank_newest_first
    FROM draft_exercise
),
drafts_to_delete AS (
    SELECT id
    FROM ranked_drafts
    WHERE rank_newest_first > 24
)
DELETE FROM draft_exercise
WHERE id IN (SELECT id FROM drafts_to_delete);

-- Show deletion results
SELECT 
    'DELETION COMPLETED' as status,
    COUNT(*) as remaining_drafts,
    COUNT(DISTINCT learning_node_id) as learning_nodes_with_drafts,
    MAX(drafts_per_node) as max_drafts_per_node
FROM (
    SELECT 
        learning_node_id,
        COUNT(*) as drafts_per_node
    FROM draft_exercise
    GROUP BY learning_node_id
) node_counts;

-- Verify no learning node has more than 24 drafts
SELECT 
    'VERIFICATION' as check_type,
    learning_node_id,
    COUNT(*) as draft_count
FROM draft_exercise
GROUP BY learning_node_id
HAVING COUNT(*) > 24
ORDER BY COUNT(*) DESC;

COMMIT;
*/

-- ============================================================================
-- STEP 3: Post-cleanup verification queries
-- ============================================================================

-- Check current distribution of drafts per learning node
SELECT 
    'CURRENT DISTRIBUTION' as info,
    draft_count,
    COUNT(*) as learning_nodes_with_this_count
FROM (
    SELECT 
        learning_node_id,
        COUNT(*) as draft_count
    FROM draft_exercise
    GROUP BY learning_node_id
) counts
GROUP BY draft_count
ORDER BY draft_count DESC;

-- Show learning nodes with the most drafts
SELECT 
    'TOP LEARNING NODES BY DRAFT COUNT' as info,
    ln.public_id as learning_node_public_id,
    ln.title as learning_node_title,
    COUNT(de.id) as draft_count
FROM learning_node ln
JOIN draft_exercise de ON ln.id = de.learning_node_id
GROUP BY ln.id, ln.public_id, ln.title
ORDER BY COUNT(de.id) DESC
LIMIT 10;
