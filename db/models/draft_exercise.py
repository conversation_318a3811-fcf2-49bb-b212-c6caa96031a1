from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Enum as SqlEnum, JSON, Text
from sqlalchemy.orm import relationship
from datetime import datetime, UTC
from enum import Enum
import uuid
from db.database import SqlAlchemyBase

class DraftExerciseStatus(str, Enum):
    NEW = "NEW"
    IN_REVIEW = "IN_REVIEW"
    ACCEPTED_BY_EDITOR = "ACCEPTED_BY_EDITOR"
    REJECTED_BY_EDITOR = "REJECTED_BY_EDITOR"
    REJECTED_BY_ADMIN = "REJECTED_BY_ADMIN"
    PUBLISHED = "PUBLISHED"

class DraftExercise(SqlAlchemyBase):
    __tablename__ = 'draft_exercise'

    id = Column(Integer, primary_key=True)
    public_id = Column(String, unique=True, nullable=False, index=True,
                       default=lambda: str(uuid.uuid4()))
    exercise_type = Column(String(50), nullable=False)
    difficulty = Column(String(20))
    data_json = Column(JSON, nullable=False)
    solution_json = Column(JSON)
    status = Column(SqlEnum(DraftExerciseStatus, name='draft_exercise_status', create_constraint=False),
                    default=DraftExerciseStatus.NEW, index=True)
    assigned_editor_id = Column(Integer, ForeignKey('editor_account.id', ondelete='SET NULL'),
                               nullable=True, index=True)
    reject_reason = Column(Text)
    published_exercise_id = Column(Integer, ForeignKey('exercise.id', ondelete='SET NULL'))
    learning_node_id = Column(Integer, ForeignKey('learning_node.id', ondelete='CASCADE'),
                             nullable=False, index=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(UTC),
                       onupdate=lambda: datetime.now(UTC))
    published_at = Column(DateTime(timezone=True))

    # Relationships
    assigned_editor = relationship("EditorAccount", back_populates="assigned_drafts")
    published_exercise = relationship("Exercise")
    learning_node = relationship("LearningNode")
    media_files = relationship("DraftMediaFile", back_populates="draft_exercise",
                              cascade="all, delete-orphan")


    @property
    def learning_nodes(self):
        """Backward compatibility property - returns list with single learning node"""
        return [self.learning_node] if self.learning_node else []

