-- Migration: Simplify Draft Exercise Learning Node Relationships
-- Description: Replace many-to-many relationship with direct foreign key
-- This migration is PostgreSQL-compatible, transaction-safe, and idempotent

BEGIN;

-- Step 1: Add learning_node_id column to draft_exercise table
ALTER TABLE draft_exercise 
ADD COLUMN IF NOT EXISTS learning_node_id INTEGER;

-- Step 2: Populate the new column with data from the association table
-- For drafts with multiple associations, take the first one (ordered by created_at)
UPDATE draft_exercise 
SET learning_node_id = (
    SELECT dlne.learning_node_id 
    FROM draft_learning_node_exercise dlne 
    WHERE dlne.draft_exercise_id = draft_exercise.id 
    ORDER BY dlne.created_at ASC 
    LIMIT 1
)
WHERE learning_node_id IS NULL 
AND EXISTS (
    SELECT 1 FROM draft_learning_node_exercise dlne 
    WHERE dlne.draft_exercise_id = draft_exercise.id
);

-- Step 3: Add foreign key constraint
ALTER TABLE draft_exercise 
ADD CONSTRAINT IF NOT EXISTS fk_draft_exercise_learning_node 
FOREIGN KEY (learning_node_id) REFERENCES learning_node(id) ON DELETE CASCADE;

-- Step 4: Add index for performance
CREATE INDEX IF NOT EXISTS idx_draft_exercise_learning_node 
ON draft_exercise(learning_node_id);

-- Step 5: Make the column NOT NULL (after data migration)
-- First check if any drafts still have NULL learning_node_id
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM draft_exercise WHERE learning_node_id IS NULL
    ) THEN
        ALTER TABLE draft_exercise 
        ALTER COLUMN learning_node_id SET NOT NULL;
    ELSE
        RAISE NOTICE 'Warning: Some draft exercises still have NULL learning_node_id. Manual cleanup required.';
    END IF;
END $$;

-- Step 6: Drop the association table and its indexes
-- This is safe because we've migrated the data
DROP TABLE IF EXISTS draft_learning_node_exercise CASCADE;

-- Step 7: Verification queries
SELECT 'Migration completed successfully' as status;

-- Count drafts with learning nodes
SELECT 
    COUNT(*) as total_drafts,
    COUNT(learning_node_id) as drafts_with_learning_nodes,
    COUNT(*) - COUNT(learning_node_id) as drafts_without_learning_nodes
FROM draft_exercise;

-- Show sample of migrated data
SELECT 
    de.public_id,
    de.exercise_type,
    de.status,
    ln.title as learning_node_title,
    ln.public_id as learning_node_public_id
FROM draft_exercise de
LEFT JOIN learning_node ln ON de.learning_node_id = ln.id
ORDER BY de.created_at DESC
LIMIT 5;

COMMIT;
