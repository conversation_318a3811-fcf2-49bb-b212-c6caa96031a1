-- Migration: Simplify Draft Exercise Learning Node Relationships
-- Description: Replace many-to-many relationship with direct foreign key
-- This migration is PostgreSQL-compatible, transaction-safe, and idempotent
-- Date: 2025-01-10
-- Author: System Migration

-- Start transaction for atomicity
BEGIN;

-- ============================================================================
-- STEP 1: PRE-MIGRATION ANALYSIS
-- ============================================================================

-- Display current state before migration
SELECT 'PRE-MIGRATION ANALYSIS' as step;

-- Count existing draft exercises
SELECT
    'Draft Exercises' as table_name,
    COUNT(*) as total_count
FROM draft_exercise;

-- Count existing associations
SELECT
    'Draft Learning Node Associations' as table_name,
    COUNT(*) as total_count
FROM draft_learning_node_exercise;

-- Check for drafts with multiple learning node associations
SELECT
    'Drafts with Multiple Learning Nodes' as analysis,
    COUNT(*) as draft_count
FROM (
    SELECT draft_exercise_id
    FROM draft_learning_node_exercise
    GROUP BY draft_exercise_id
    HAVING COUNT(*) > 1
) multi_associations;

-- Show drafts with multiple associations (for review)
SELECT
    de.public_id as draft_public_id,
    de.exercise_type,
    de.status,
    COUNT(dlne.learning_node_id) as learning_node_count,
    STRING_AGG(ln.title, ', ' ORDER BY dlne.created_at) as learning_node_titles
FROM draft_exercise de
JOIN draft_learning_node_exercise dlne ON de.id = dlne.draft_exercise_id
JOIN learning_node ln ON dlne.learning_node_id = ln.id
GROUP BY de.id, de.public_id, de.exercise_type, de.status
HAVING COUNT(dlne.learning_node_id) > 1
ORDER BY COUNT(dlne.learning_node_id) DESC;

-- ============================================================================
-- STEP 2: ADD NEW COLUMN
-- ============================================================================

SELECT 'ADDING LEARNING_NODE_ID COLUMN' as step;

-- Add learning_node_id column to draft_exercise table (if not exists)
ALTER TABLE draft_exercise
ADD COLUMN IF NOT EXISTS learning_node_id INTEGER;

-- ============================================================================
-- STEP 3: DATA MIGRATION
-- ============================================================================

SELECT 'MIGRATING DATA FROM ASSOCIATION TABLE' as step;

-- Populate the new column with data from the association table
-- For drafts with multiple associations, take the first one (ordered by created_at)
UPDATE draft_exercise
SET learning_node_id = (
    SELECT dlne.learning_node_id
    FROM draft_learning_node_exercise dlne
    WHERE dlne.draft_exercise_id = draft_exercise.id
    ORDER BY dlne.created_at ASC
    LIMIT 1
)
WHERE learning_node_id IS NULL
AND EXISTS (
    SELECT 1 FROM draft_learning_node_exercise dlne
    WHERE dlne.draft_exercise_id = draft_exercise.id
);

-- Report migration results
SELECT
    'Data Migration Results' as step,
    COUNT(*) as total_drafts,
    COUNT(learning_node_id) as drafts_with_learning_nodes,
    COUNT(*) - COUNT(learning_node_id) as drafts_without_learning_nodes
FROM draft_exercise;

-- ============================================================================
-- STEP 4: VALIDATION
-- ============================================================================

SELECT 'VALIDATING MIGRATED DATA' as step;

-- Check for any drafts that couldn't be migrated
SELECT
    de.public_id,
    de.exercise_type,
    de.status,
    de.created_at
FROM draft_exercise de
WHERE de.learning_node_id IS NULL
ORDER BY de.created_at;

-- Verify all learning_node_id values reference valid learning nodes
SELECT
    'Invalid Learning Node References' as validation,
    COUNT(*) as count
FROM draft_exercise de
WHERE de.learning_node_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM learning_node ln WHERE ln.id = de.learning_node_id
);

-- ============================================================================
-- STEP 5: ADD CONSTRAINTS AND INDEXES
-- ============================================================================

SELECT 'ADDING CONSTRAINTS AND INDEXES' as step;

-- Add foreign key constraint
ALTER TABLE draft_exercise
ADD CONSTRAINT IF NOT EXISTS fk_draft_exercise_learning_node
FOREIGN KEY (learning_node_id) REFERENCES learning_node(id) ON DELETE CASCADE;

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_draft_exercise_learning_node
ON draft_exercise(learning_node_id);

-- ============================================================================
-- STEP 6: MAKE COLUMN NOT NULL (if all data migrated successfully)
-- ============================================================================

SELECT 'SETTING NOT NULL CONSTRAINT' as step;

-- Check if we can safely make the column NOT NULL
DO $$
DECLARE
    null_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO null_count
    FROM draft_exercise
    WHERE learning_node_id IS NULL;

    IF null_count = 0 THEN
        ALTER TABLE draft_exercise
        ALTER COLUMN learning_node_id SET NOT NULL;
        RAISE NOTICE 'Successfully set learning_node_id as NOT NULL';
    ELSE
        RAISE WARNING 'Cannot set NOT NULL: % draft exercises still have NULL learning_node_id', null_count;
        RAISE NOTICE 'Manual cleanup required for drafts without learning node associations';
    END IF;
END $$;

-- ============================================================================
-- STEP 7: BACKUP ASSOCIATION DATA (before dropping table)
-- ============================================================================

SELECT 'CREATING BACKUP OF ASSOCIATION DATA' as step;

-- Create a backup table with association data for safety
CREATE TABLE IF NOT EXISTS draft_learning_node_exercise_backup AS
SELECT
    dlne.*,
    de.public_id as draft_public_id,
    ln.title as learning_node_title,
    ln.public_id as learning_node_public_id,
    CURRENT_TIMESTAMP as backup_created_at
FROM draft_learning_node_exercise dlne
JOIN draft_exercise de ON dlne.draft_exercise_id = de.id
JOIN learning_node ln ON dlne.learning_node_id = ln.id;

-- Report backup creation
SELECT
    'Backup Table Created' as step,
    COUNT(*) as backed_up_associations
FROM draft_learning_node_exercise_backup;

-- ============================================================================
-- STEP 8: DROP OLD ASSOCIATION TABLE
-- ============================================================================

SELECT 'DROPPING OLD ASSOCIATION TABLE' as step;

-- Drop the association table and its indexes (CASCADE removes dependent objects)
DROP TABLE IF EXISTS draft_learning_node_exercise CASCADE;

-- ============================================================================
-- STEP 9: FINAL VERIFICATION
-- ============================================================================

SELECT 'FINAL VERIFICATION' as step;

-- Verify the migration was successful
SELECT
    'Migration Summary' as summary,
    COUNT(*) as total_drafts,
    COUNT(learning_node_id) as drafts_with_learning_nodes,
    MIN(created_at) as oldest_draft,
    MAX(created_at) as newest_draft
FROM draft_exercise;

-- Show sample of migrated data with learning node details
SELECT
    'Sample Migrated Data' as verification,
    de.public_id as draft_id,
    de.exercise_type,
    de.status,
    ln.title as learning_node_title,
    ln.public_id as learning_node_id,
    ch.title as chapter_title,
    s.name as subject_name
FROM draft_exercise de
JOIN learning_node ln ON de.learning_node_id = ln.id
JOIN chapter ch ON ln.chapter_id = ch.id
JOIN subject s ON ch.subject_id = s.id
ORDER BY de.created_at DESC
LIMIT 10;

-- Check for any orphaned data
SELECT
    'Orphaned Drafts Check' as check_type,
    COUNT(*) as orphaned_count
FROM draft_exercise de
WHERE de.learning_node_id IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM learning_node ln WHERE ln.id = de.learning_node_id
);

SELECT 'MIGRATION COMPLETED SUCCESSFULLY' as final_status;

-- Commit the transaction
COMMIT;

-- ============================================================================
-- POST-MIGRATION NOTES
-- ============================================================================

/*
POST-MIGRATION NOTES:

1. BACKUP DATA: The old association data has been backed up in
   'draft_learning_node_exercise_backup' table. You can drop this table
   after confirming the migration was successful.

2. MULTIPLE ASSOCIATIONS: If any drafts had multiple learning node associations,
   only the first one (by created_at) was migrated. The backup table contains
   all original associations for reference.

3. ROLLBACK: If you need to rollback this migration, you can:
   - Recreate the draft_learning_node_exercise table
   - Restore data from the backup table
   - Remove the learning_node_id column from draft_exercise

4. APPLICATION CODE: Ensure your application code is updated to use the new
   direct relationship before deploying.

5. CLEANUP: After confirming everything works correctly, you can drop the
   backup table:
   DROP TABLE draft_learning_node_exercise_backup;
*/
