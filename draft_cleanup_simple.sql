-- ============================================================================
-- SIMPLE DRAFT EXERCISE CLEANUP: Limit to 24 NEW drafts per learning node
-- ============================================================================
-- SAFETY: Only deletes drafts with status = 'NEW' to preserve work in progress

-- STEP 1: PREVIEW what will be deleted (RUN THIS FIRST!)
-- ============================================================================

-- First, see current status distribution
SELECT
    'CURRENT STATUS DISTRIBUTION' as info,
    status,
    COUNT(*) as count,
    ROUND(100.0 * COUNT(*) / SUM(COUNT(*)) OVER(), 2) as percentage
FROM draft_exercise
GROUP BY status
ORDER BY count DESC;
WITH ranked_new_drafts AS (
    SELECT
        de.id,
        de.public_id,
        de.learning_node_id,
        de.status,
        de.created_at,
        ln.title as learning_node_title,
        ROW_NUMBER() OVER (
            PARTITION BY de.learning_node_id
            ORDER BY de.created_at DESC, de.id DESC
        ) as rank_newest_first
    FROM draft_exercise de
    JOIN learning_node ln ON de.learning_node_id = ln.id
    WHERE de.status = 'NEW'  -- Only consider NEW drafts
)
SELECT
    learning_node_title,
    COUNT(*) as new_drafts_to_delete,
    'NEW' as status_affected
FROM ranked_new_drafts
WHERE rank_newest_first > 24
GROUP BY learning_node_id, learning_node_title
ORDER BY new_drafts_to_delete DESC;

-- STEP 2: ACTUAL DELETION (Uncomment and run after reviewing preview)
-- ============================================================================
/*
BEGIN;

WITH ranked_new_drafts AS (
    SELECT
        id,
        ROW_NUMBER() OVER (
            PARTITION BY learning_node_id
            ORDER BY created_at DESC, id DESC
        ) as rank_newest_first
    FROM draft_exercise
    WHERE status = 'NEW'  -- Only rank NEW drafts
)
DELETE FROM draft_exercise
WHERE id IN (
    SELECT id
    FROM ranked_new_drafts
    WHERE rank_newest_first > 24
);

-- Show results
SELECT
    'Cleanup completed - NEW drafts only' as status,
    COUNT(*) as remaining_total_drafts,
    COUNT(CASE WHEN status = 'NEW' THEN 1 END) as remaining_new_drafts,
    MAX(new_draft_count) as max_new_drafts_per_node
FROM draft_exercise de
JOIN (
    SELECT
        learning_node_id,
        COUNT(*) as new_draft_count
    FROM draft_exercise
    WHERE status = 'NEW'
    GROUP BY learning_node_id
) counts ON de.learning_node_id = counts.learning_node_id;

COMMIT;
*/
