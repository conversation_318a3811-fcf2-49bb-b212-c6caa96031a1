-- ============================================================================
-- SIMPLE DRAFT EXERCISE CLEANUP: Limit to 24 drafts per learning node
-- ============================================================================

-- STEP 1: PREVIEW what will be deleted (RUN THIS FIRST!)
-- ============================================================================
WITH ranked_drafts AS (
    SELECT 
        de.id,
        de.public_id,
        de.learning_node_id,
        de.status,
        de.created_at,
        ln.title as learning_node_title,
        ROW_NUMBER() OVER (
            PARTITION BY de.learning_node_id 
            ORDER BY de.created_at DESC, de.id DESC
        ) as rank_newest_first
    FROM draft_exercise de
    JOIN learning_node ln ON de.learning_node_id = ln.id
)
SELECT 
    learning_node_title,
    COUNT(*) as drafts_to_delete,
    STRING_AGG(DISTINCT status::text, ', ') as statuses_affected
FROM ranked_drafts
WHERE rank_newest_first > 24
GROUP BY learning_node_id, learning_node_title
ORDER BY drafts_to_delete DESC;

-- STEP 2: ACTUAL DELETION (Uncomment and run after reviewing preview)
-- ============================================================================
/*
BEGIN;

WITH ranked_drafts AS (
    SELECT 
        id,
        ROW_NUMBER() OVER (
            PARTITION BY learning_node_id 
            ORDER BY created_at DESC, id DESC
        ) as rank_newest_first
    FROM draft_exercise
)
DELETE FROM draft_exercise
WHERE id IN (
    SELECT id 
    FROM ranked_drafts 
    WHERE rank_newest_first > 24
);

-- Show results
SELECT 
    'Cleanup completed' as status,
    COUNT(*) as remaining_drafts,
    MAX(draft_count) as max_drafts_per_node
FROM (
    SELECT COUNT(*) as draft_count
    FROM draft_exercise
    GROUP BY learning_node_id
) counts;

COMMIT;
*/
